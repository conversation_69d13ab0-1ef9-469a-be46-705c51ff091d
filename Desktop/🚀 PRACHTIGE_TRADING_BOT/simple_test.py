print("Hello from Python!")
print("Testing basic functionality...")

# Test basic imports
try:
    import sys
    import os
    from pathlib import Path
    print("✅ Basic imports work")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")

# Test project structure
try:
    project_root = Path(__file__).parent
    print(f"✅ Project root: {project_root}")
    
    # Check if core directory exists
    core_dir = project_root / "core"
    if core_dir.exists():
        print("✅ Core directory exists")
    else:
        print("❌ Core directory missing")
        
    # List files in core
    if core_dir.exists():
        files = list(core_dir.glob("*.py"))
        print(f"✅ Found {len(files)} Python files in core/")
        for f in files:
            print(f"  - {f.name}")
            
except Exception as e:
    print(f"❌ Project structure test failed: {e}")

print("Test completed!")
