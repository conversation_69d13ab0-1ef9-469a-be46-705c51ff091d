"""
🚀 PRACHTIGE TRADING BOT - SCHEDULER
===================================

Automatische scheduler voor dagelijkse broadcasts en marktanalyse.
Verstuurt dagelijks om 09:00 marktanalyse naar alle geregistreerde gebruikers.

Author: Innovars Lab
Version: 2.0.0
"""

import asyncio
import logging
import schedule
import time
from datetime import datetime, timedelta
from threading import Thread
from broadcast_system import BroadcastSystem

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TradingBotScheduler:
    """
    🕐 Scheduler voor automatische broadcasts
    """
    
    def __init__(self, broadcast_system: BroadcastSystem):
        """Initialize scheduler"""
        self.broadcast_system = broadcast_system
        self.logger = logging.getLogger(__name__)
        self.running = False
        
        self.logger.info("🕐 Trading Bot Scheduler geïnitialiseerd!")
    
    def setup_schedule(self):
        """Setup scheduled tasks"""
        # Daily market analysis at 09:00
        schedule.every().day.at("09:00").do(self.run_daily_analysis)
        
        # Market alerts every 4 hours
        schedule.every(4).hours.do(self.run_market_alerts)
        
        # Weekly summary on Sunday at 20:00
        schedule.every().sunday.at("20:00").do(self.run_weekly_summary)
        
        # Test broadcast every minute (for testing)
        # schedule.every(1).minutes.do(self.run_test_broadcast)
        
        self.logger.info("📅 Scheduled tasks configured:")
        self.logger.info("   ├─ 🌅 Daily Analysis: 09:00")
        self.logger.info("   ├─ 🚨 Market Alerts: Every 4 hours")
        self.logger.info("   └─ 📊 Weekly Summary: Sunday 20:00")
    
    def run_daily_analysis(self):
        """Run daily market analysis broadcast"""
        self.logger.info("🌅 Starting daily market analysis broadcast...")
        
        try:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            sent_count, failed_count = loop.run_until_complete(
                self.broadcast_system.send_daily_analysis()
            )
            
            loop.close()
            
            self.logger.info(f"✅ Daily analysis completed: {sent_count} sent, {failed_count} failed")
            
        except Exception as e:
            self.logger.error(f"❌ Daily analysis failed: {e}")
    
    def run_market_alerts(self):
        """Run market alerts for significant price movements"""
        self.logger.info("🚨 Checking for market alerts...")
        
        try:
            # Mock implementation - in real app, check for significant price changes
            # For now, we'll send alerts randomly to simulate market movements
            import random
            
            if random.choice([True, False, False, False]):  # 25% chance
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                loop.run_until_complete(self.send_market_alert())
                loop.close()
                
                self.logger.info("🚨 Market alert sent!")
            else:
                self.logger.info("📊 No significant market movements detected")
                
        except Exception as e:
            self.logger.error(f"❌ Market alerts failed: {e}")
    
    def run_weekly_summary(self):
        """Run weekly trading summary"""
        self.logger.info("📊 Starting weekly summary broadcast...")
        
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            loop.run_until_complete(self.send_weekly_summary())
            loop.close()
            
            self.logger.info("✅ Weekly summary completed!")
            
        except Exception as e:
            self.logger.error(f"❌ Weekly summary failed: {e}")
    
    def run_test_broadcast(self):
        """Run test broadcast (for development)"""
        self.logger.info("🧪 Running test broadcast...")
        
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            loop.run_until_complete(self.send_test_message())
            loop.close()
            
        except Exception as e:
            self.logger.error(f"❌ Test broadcast failed: {e}")
    
    async def send_market_alert(self):
        """Send market alert for significant movements"""
        import random
        
        # Mock alert data
        alerts = [
            {
                "symbol": "BTC",
                "change": 8.5,
                "price": 45200,
                "type": "surge"
            },
            {
                "symbol": "ETH", 
                "change": -6.2,
                "price": 2520,
                "type": "drop"
            },
            {
                "symbol": "SOL",
                "change": 12.3,
                "price": 110,
                "type": "surge"
            }
        ]
        
        alert = random.choice(alerts)
        
        if alert["type"] == "surge":
            emoji = "🚀"
            action = "BUY"
            message = "STRONG BULLISH MOVEMENT"
        else:
            emoji = "📉"
            action = "SELL"
            message = "SIGNIFICANT DROP DETECTED"
        
        text = f"""
🚨 **MARKET ALERT** 🚨

{emoji} **{alert['symbol']} {message}** {emoji}

📊 **Alert Details:**
├─ 💰 Current Price: ${alert['price']:,.2f}
├─ 📈 Change: {alert['change']:+.1f}%
├─ ⏰ Time: {datetime.now().strftime('%H:%M:%S')}
└─ 🎯 Suggested Action: {action}

🔥 **QUICK ACTION REQUIRED!**
Market is moving fast - consider taking action now!
        """
        
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        keyboard = [
            [
                InlineKeyboardButton(f"🟢 BUY {alert['symbol']}", callback_data=f"trade_buy_{alert['symbol']}"),
                InlineKeyboardButton(f"🔴 SELL {alert['symbol']}", callback_data=f"trade_sell_{alert['symbol']}")
            ],
            [InlineKeyboardButton("📊 Full Analysis", callback_data="detailed_analysis")]
        ]
        
        # Send to all users with market alerts enabled
        sent_count = 0
        for user_id_str, user_data in self.broadcast_system.subscribers["users"].items():
            if user_data["preferences"]["market_alerts"]:
                try:
                    await self.broadcast_system.main_bot.send_message(
                        chat_id=int(user_id_str),
                        text=text,
                        reply_markup=InlineKeyboardMarkup(keyboard),
                        parse_mode="Markdown"
                    )
                    sent_count += 1
                    await asyncio.sleep(0.1)
                except Exception as e:
                    self.logger.warning(f"⚠️ Failed to send alert to {user_id_str}: {e}")
        
        self.logger.info(f"🚨 Market alert sent to {sent_count} users")
    
    async def send_weekly_summary(self):
        """Send weekly trading summary"""
        text = f"""
📊 **WEEKLY TRADING SUMMARY** 📊
🗓️ **Week: {datetime.now().strftime('%d-%m-%Y')}**

🏆 **Top Performers This Week:**
├─ 🥇 Bitcoin (BTC): +15.2%
├─ 🥈 Solana (SOL): +12.8%
└─ 🥉 Ethereum (ETH): ****%

📈 **Market Highlights:**
├─ 💰 Total Market Cap: $1.85T (****%)
├─ 📊 Weekly Volume: $2.1T
├─ 😊 Fear & Greed Index: 78 (Extreme Greed)
└─ 🚀 Trending: DeFi tokens surge

🎯 **Your Trading Stats:**
├─ 📊 Total Trades: 12
├─ ✅ Successful: 9 (75%)
├─ 💰 Total Volume: $3,250
└─ 📈 Estimated Profit: +$420 (+12.9%)

**Great week! Keep up the excellent trading!** 🚀
        """
        
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        keyboard = [
            [InlineKeyboardButton("📊 Detailed Stats", callback_data="detailed_stats")],
            [InlineKeyboardButton("🚀 Start New Week", callback_data="get_analysis_now")]
        ]
        
        # Send to all users
        sent_count = 0
        for user_id_str in self.broadcast_system.subscribers["users"]:
            try:
                await self.broadcast_system.main_bot.send_message(
                    chat_id=int(user_id_str),
                    text=text,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode="Markdown"
                )
                sent_count += 1
                await asyncio.sleep(0.1)
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to send summary to {user_id_str}: {e}")
        
        self.logger.info(f"📊 Weekly summary sent to {sent_count} users")
    
    async def send_test_message(self):
        """Send test message (for development)"""
        text = f"""
🧪 **TEST BROADCAST** 🧪
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}

This is a test message to verify the broadcast system is working correctly.

**System Status:** ✅ Online
        """
        
        # Send to first user only for testing
        if self.broadcast_system.subscribers["users"]:
            first_user = list(self.broadcast_system.subscribers["users"].keys())[0]
            try:
                await self.broadcast_system.main_bot.send_message(
                    chat_id=int(first_user),
                    text=text,
                    parse_mode="Markdown"
                )
                self.logger.info("🧪 Test message sent successfully")
            except Exception as e:
                self.logger.warning(f"⚠️ Test message failed: {e}")
    
    def start(self):
        """Start the scheduler"""
        self.running = True
        self.setup_schedule()
        
        def run_scheduler():
            while self.running:
                schedule.run_pending()
                time.sleep(1)
        
        # Run scheduler in separate thread
        scheduler_thread = Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        self.logger.info("🚀 Scheduler started successfully!")
        return scheduler_thread
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        schedule.clear()
        self.logger.info("⏹️ Scheduler stopped")

# Main function for testing
if __name__ == "__main__":
    # Initialize broadcast system
    broadcast_system = BroadcastSystem()
    
    # Initialize scheduler
    scheduler = TradingBotScheduler(broadcast_system)
    
    # Start scheduler
    scheduler.start()
    
    try:
        # Keep running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        scheduler.stop()
        print("🛑 Scheduler stopped by user")
