"""
Telegram handlers for the Prachtige Trading Bot.
Contains all command and callback handlers for the Telegram bot.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
try:
    from telegram.constants import ParseMode
except ImportError:
    # Fallback for older versions
    from telegram import ParseMode
from telegram.ext import ContextTypes

# Initialize logger
logger = logging.getLogger(__name__)

# Bot manager reference
bot_manager = None

# Broadcast system reference
broadcast_system = None

def set_bot_manager(manager):
    """Set the bot manager reference"""
    global bot_manager
    bot_manager = manager

def set_broadcast_system(system):
    """Set the broadcast system reference"""
    global broadcast_system
    broadcast_system = system

# Helper functions
def format_currency(amount: float) -> str:
    """Format amount as currency"""
    return f"${amount:,.2f}"

def format_percentage(value: float) -> str:
    """Format value as percentage"""
    return f"{value:.1f}%"

# Global references for onboarding system
onboarding_manager = None
progressive_keyboards = None

def set_onboarding_system(onboarding_mgr, prog_keyboards):
    """Set onboarding system references"""
    global onboarding_manager, progressive_keyboards
    onboarding_manager = onboarding_mgr
    progressive_keyboards = prog_keyboards

# Command handlers
async def start_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command - Always show working interface"""
    user = update.effective_user
    user_id = user.id

    try:
        logger.info(f"🚀 Start command from user {user_id} (@{user.username})")

        # Register user in user manager first
        if bot_manager and bot_manager.user_manager:
            await bot_manager.user_manager.register_user(
                user_id=user_id,
                username=user.username or "unknown",
                first_name=user.first_name or "User",
                last_name=user.last_name
            )
            logger.info(f"✅ User {user_id} registered successfully")

        # Register user for broadcasts
        if broadcast_system:
            user_data = {
                "username": user.username or "unknown",
                "first_name": user.first_name or "User",
                "last_name": user.last_name
            }
            is_new_user = broadcast_system.register_user(user_id, user_data)
            logger.info(f"✅ User {user_id} registered for broadcasts")

        # Always show working main interface with guaranteed buttons
        await _send_guaranteed_interface(update, context)

    except Exception as e:
        logger.error(f"❌ Error in start handler for user {user_id}: {e}")
        import traceback
        logger.error(f"❌ Full traceback: {traceback.format_exc()}")
        # Fallback to basic welcome with buttons
        await _send_emergency_welcome(update, context)

async def _send_guaranteed_interface(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send guaranteed interface with buttons that always works"""
    user = update.effective_user
    user_id = user.id

    # Create welcome text
    welcome_text = f"""
🚀 **WELKOM BIJ INNOVARS TRADING BOT!** 🚀

👋 Hallo {user.first_name}!

🤖 **Wat kan deze bot?**
✅ Paper Trading met $10,000 virtueel geld
✅ Cryptocurrency marktdata en prijzen
✅ Trading signalen en analyses
✅ Portfolio tracking en management
✅ Community toegang voor premium leden

⚠️ **Disclaimer:** Wij zijn geen financiële adviseurs. Trading brengt risico's met zich mee.

🎯 **Kies een optie:**
    """

    # Always create working buttons
    keyboard = [
        [InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu"),
         InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
        [InlineKeyboardButton("🔔 Basis Signalen", callback_data="signals_basic"),
         InlineKeyboardButton("📰 Crypto Nieuws", callback_data="news_basic")],
        [InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")],
        [InlineKeyboardButton("⚙️ Instellingen", callback_data="settings_basic"),
         InlineKeyboardButton("❓ Help", callback_data="help_basic")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(
        welcome_text,
        reply_markup=reply_markup,
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_working_main_interface(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send guaranteed working main interface with all buttons"""
    user = update.effective_user
    user_id = user.id

    # Check user status
    is_admin = False
    is_premium = False

    if bot_manager and bot_manager.user_manager:
        try:
            is_admin = await bot_manager.user_manager.is_admin(user_id)
            is_premium = await bot_manager.user_manager.has_paid_premium(user_id)
        except:
            pass

    # Create welcome text
    welcome_text = f"""
🚀 **WELKOM BIJ INNOVARS TRADING BOT!** 🚀

👋 Hallo {user.first_name}!

🤖 **Wat kan deze bot?**
✅ Paper Trading met $10,000 virtueel geld
✅ Cryptocurrency marktdata en prijzen
✅ Trading signalen en analyses
✅ Portfolio tracking en management
✅ Community toegang voor premium leden

💰 **Je Status:** {'👑 Admin' if is_admin else '💎 Premium' if is_premium else '🆓 Gratis'}

⚠️ **Disclaimer:** Wij zijn geen financiële adviseurs. Trading brengt risico's met zich mee.

🎯 **Kies een optie:**
    """

    # Create appropriate keyboard based on user status
    if is_admin:
        keyboard = [
            [InlineKeyboardButton("👑 Admin Panel", callback_data="admin_panel")],
            [InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu"),
             InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
            [InlineKeyboardButton("🤖 AI Trading", callback_data="ai_trading"),
             InlineKeyboardButton("💬 Community", callback_data="premium_community")],
            [InlineKeyboardButton("📊 Analytics", callback_data="premium_analytics"),
             InlineKeyboardButton("⚙️ Instellingen", callback_data="settings_premium")]
        ]
    elif is_premium:
        keyboard = [
            [InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu"),
             InlineKeyboardButton("🤖 AI Trading", callback_data="ai_trading")],
            [InlineKeyboardButton("💬 Community", callback_data="premium_community"),
             InlineKeyboardButton("📊 Analytics", callback_data="premium_analytics")],
            [InlineKeyboardButton("🔔 Premium Signalen", callback_data="premium_signals"),
             InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
            [InlineKeyboardButton("⚙️ Instellingen", callback_data="settings_premium"),
             InlineKeyboardButton("❓ Help", callback_data="help_premium")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu"),
             InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
            [InlineKeyboardButton("🔔 Basis Signalen", callback_data="signals_basic"),
             InlineKeyboardButton("📰 Crypto Nieuws", callback_data="news_basic")],
            [InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")],
            [InlineKeyboardButton("⚙️ Instellingen", callback_data="settings_basic"),
             InlineKeyboardButton("❓ Help", callback_data="help_basic")]
        ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(
        welcome_text,
        reply_markup=reply_markup,
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_main_interface(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send main interface for users who completed onboarding"""
    user = update.effective_user
    user_id = user.id

    try:
        # Get user status
        is_premium = False
        is_admin = False

        if bot_manager and bot_manager.user_manager:
            is_premium = await bot_manager.user_manager.has_paid_premium(user_id)
            is_admin = await bot_manager.user_manager.is_admin(user_id)

        # Create personalized welcome message
        if is_admin:
            welcome_text = f"""
👑 **ADMIN DASHBOARD** 👑

Welkom terug, {user.first_name}!

🔧 **Admin Features:**
• User management
• System analytics
• Broadcast controls
• Premium features
            """
        elif is_premium:
            welcome_text = f"""
💎 **PREMIUM DASHBOARD** 💎

Welkom terug, {user.first_name}!

🚀 **Premium Features:**
• AI Trading Assistant
• Community Access
• Advanced Analytics
• Premium Signals
            """
        else:
            welcome_text = f"""
🆓 **FREE DASHBOARD** 🆓

Welkom terug, {user.first_name}!

📊 **Available Features:**
• Basic Portfolio View
• Market Data
• Basic Signals
• Upgrade to Premium
            """

        # Get appropriate keyboard
        keyboard = None
        if progressive_keyboards:
            keyboard = await progressive_keyboards.get_main_keyboard(user_id)

        # Fallback keyboard if progressive keyboards fail
        if not keyboard:
            if is_admin:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("👑 Admin Panel", callback_data="admin_panel")],
                    [InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu")],
                    [InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")]
                ])
            elif is_premium:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("🤖 AI Trading", callback_data="ai_trading")],
                    [InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu")],
                    [InlineKeyboardButton("💬 Community", callback_data="premium_community")],
                    [InlineKeyboardButton("📈 Analytics", callback_data="premium_analytics")]
                ])
            else:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu")],
                    [InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
                    [InlineKeyboardButton("💎 Upgrade Premium", callback_data="show_premium_options")],
                    [InlineKeyboardButton("❓ Help", callback_data="help_basic")]
                ])

        # Send main message
        await update.message.reply_text(
            welcome_text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

        # Add persistent keyboard if available
        if progressive_keyboards:
            persistent_keyboard = await progressive_keyboards.get_persistent_keyboard(user_id)
            if persistent_keyboard:
                await update.message.reply_text(
                    "🎯 **Snelle toegang:**",
                    reply_markup=persistent_keyboard,
                    parse_mode=ParseMode.MARKDOWN
                )

    except Exception as e:
        logger.error(f"❌ Error sending main interface: {e}")
        await _send_basic_welcome(update, context)

async def _send_basic_welcome(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Fallback basic welcome message"""
    user = update.effective_user

    text = f"""
🚀 **WELKOM BIJ PRACHTIGE TRADING BOT!** 🚀

Hallo {user.first_name}!

Er is een probleem opgetreden bij het laden van je dashboard.
Probeer het later opnieuw of neem contact op met support.

Typ /help voor meer informatie.
    """

    await update.message.reply_text(text, parse_mode=ParseMode.MARKDOWN)

async def _send_emergency_welcome(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Emergency welcome with basic buttons when everything else fails"""
    user = update.effective_user

    text = f"""
🚀 **WELKOM BIJ INNOVARS TRADING BOT!** 🚀

Hallo {user.first_name}! 👋

🤖 **Wat kan deze bot?**
✅ Paper Trading met $10,000 virtueel geld
✅ Cryptocurrency marktdata en prijzen
✅ Trading signalen en analyses
✅ Portfolio tracking en management
✅ Community toegang voor premium leden

⚠️ **Disclaimer:** Wij zijn geen financiële adviseurs. Trading brengt risico's met zich mee.

🎯 **Kies een optie om te beginnen:**
    """

    keyboard = [
        [InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu")],
        [InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
        [InlineKeyboardButton("💎 Premium Info", callback_data="show_premium_options")],
        [InlineKeyboardButton("❓ Help", callback_data="help_basic")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(
        text,
        reply_markup=reply_markup,
        parse_mode=ParseMode.MARKDOWN
    )

async def help_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /help command"""
    text = """
❓ **HELP & SUPPORT** ❓

🤖 **Bot Commands:**
├─ /start - Start the bot
├─ /help - Show this help message
├─ /register - Configure API keys
├─ /trading - Trading dashboard
└─ /admin - Admin panel (admins only)

🔧 **Common Issues:**
├─ API keys not working? Double-check for typos
├─ Bot not responding? Try /start to reset
├─ Trades not executing? Check your balance
└─ Need more help? Contact support

**Need assistance? Contact us:**
<EMAIL>
    """

    keyboard = [
        [InlineKeyboardButton("📚 User Guide", callback_data="user_guide")],
        [InlineKeyboardButton("🔧 Troubleshooting", callback_data="troubleshooting")],
        [InlineKeyboardButton("💬 Contact Support", callback_data="contact_support")],
        [InlineKeyboardButton("🔙 Back to Main", callback_data="user_main")]
    ]

    await update.message.reply_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def register_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /register command"""
    from .keyboards import get_exchange_keyboard

    text = """
🔑 **API KEY REGISTRATION** 🔑

To enable trading, you need to connect your exchange API keys.

⚠️ **Security Notes:**
├─ We encrypt all API keys
├─ Enable "Read + Trade" permissions only
├─ Disable withdrawals for security
└─ Never share your API keys with anyone

**Select an exchange to connect:**
    """

    await update.message.reply_text(
        text,
        reply_markup=get_exchange_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def trading_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /trading command"""
    from .keyboards import get_trading_keyboard

    text = """
📈 **TRADING DASHBOARD** 📈

Welcome to your trading control center!

🚀 **Trading Actions:**
├─ 💰 View Portfolio
├─ 📊 Trading Signals
├─ 🤖 Auto Trading
├─ 📈 Manual Trading
└─ 📉 Open Positions

**Select an option:**
    """

    await update.message.reply_text(
        text,
        reply_markup=get_trading_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def admin_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /admin command"""
    user_id = update.effective_user.id

    # Check if user is admin
    is_admin = await bot_manager.user_manager.is_admin(user_id)

    if is_admin:
        from .keyboards import get_admin_keyboard

        text = """
👑 **ADMIN DASHBOARD** 👑

Welcome to the admin control panel!

🔧 **Admin Actions:**
├─ 👥 User Management
├─ 📊 System Analytics
├─ 📢 Broadcast Messages
├─ ⚙️ Bot Settings
└─ 💹 Trading Controls

**Select an option:**
        """

        await update.message.reply_text(
            text,
            reply_markup=get_admin_keyboard(),
            parse_mode=ParseMode.MARKDOWN
        )
    else:
        await update.message.reply_text(
            "❌ You don't have permission to access the admin panel."
        )

async def callback_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle callback queries from inline keyboards"""
    query = update.callback_query
    await query.answer()

    # Get callback data
    data = query.data

    # Try onboarding callbacks first
    if onboarding_manager:
        onboarding_handled = await onboarding_manager.handle_onboarding_callback(update, context)
        if onboarding_handled:
            return  # Onboarding system handled the callback

    # Process callback based on data
    if data == "user_main" or data == "main_menu":
        await _send_main_menu(update, context)
    elif data == "trading_dashboard":
        await _send_trading_dashboard(update, context)
    elif data == "user_portfolio" or data.startswith("portfolio_"):
        await _handle_portfolio_callback(update, context, data)
    elif data == "trading_signals" or data.startswith("signals_"):
        await _handle_signals_callback(update, context, data)
    elif data == "market_data" or data.startswith("market_"):
        await _handle_market_callback(update, context, data)
    elif data == "ai_assistant" or data.startswith("ai_"):
        await _send_ai_assistant(update, context)
    elif data == "community_chat" or data == "premium_community":
        await _send_community_chat(update, context)
    elif data == "learning_center":
        await _send_learning_center(update, context)
    elif data == "support_center":
        await _send_support_center(update, context)
    elif data == "about_bot":
        await _send_about_bot(update, context)
    elif data == "toggle_auto_trading":
        await _toggle_auto_trading(update, context)
    elif data == "account_settings" or data.startswith("settings_"):
        await _handle_settings_callback(update, context, data)
    elif data == "admin_analytics":
        await _send_analytics_dashboard(update, context)
    # Market Data callbacks
    elif data.startswith("market_"):
        await _handle_market_data_callback(update, context, data)
    # AI Assistant callbacks
    elif data.startswith("ai_"):
        await _handle_ai_callback(update, context, data)
    # Learning callbacks
    elif data.startswith("learn_"):
        await _handle_learning_callback(update, context, data)
    # Support callbacks
    elif data.startswith("support_"):
        await _handle_support_callback(update, context, data)
    # Settings callbacks
    elif data.startswith("settings_"):
        await _handle_settings_callback(update, context, data)
    # Trading callbacks from broadcast system
    elif data.startswith("trade_"):
        await _handle_trading_callback(update, context, data)
    elif data.startswith("amount_"):
        await _handle_amount_callback(update, context, data)
    elif data.startswith("confirm_"):
        await _handle_confirm_callback(update, context, data)
    elif data.startswith("cancel_"):
        await _handle_cancel_callback(update, context, data)
    elif data.startswith("custom_amount_"):
        await _handle_custom_amount_callback(update, context, data)
    elif data.startswith("back_amount_"):
        await _handle_back_amount_callback(update, context, data)
    # Broadcast system callbacks
    elif data == "detailed_analysis":
        await _handle_detailed_analysis(update, context)
    elif data == "broadcast_settings":
        await _handle_broadcast_settings(update, context)
    elif data == "get_analysis_now":
        await _handle_detailed_analysis(update, context)
    # Paper Trading callbacks
    elif data == "paper_trading_menu" or data == "paper_trading":
        await _handle_paper_trading_menu(update, context)
    elif data == "paper_buy":
        await _handle_paper_buy_menu(update, context)
    elif data == "paper_sell":
        await _handle_paper_sell_menu(update, context)
    elif data.startswith("paper_buy_"):
        symbol = data.replace("paper_buy_", "")
        await _handle_paper_buy_crypto(update, context, symbol)
    elif data.startswith("paper_sell_"):
        symbol = data.replace("paper_sell_", "")
        await _handle_paper_sell_crypto(update, context, symbol)
    elif data.startswith("paper_execute_buy_"):
        parts = data.replace("paper_execute_buy_", "").split("_")
        symbol, amount = parts[0], float(parts[1])
        await _handle_paper_execute_buy(update, context, symbol, amount)
    elif data.startswith("paper_execute_sell_"):
        parts = data.replace("paper_execute_sell_", "").split("_")
        symbol, amount = parts[0], float(parts[1])
        await _handle_paper_execute_sell(update, context, symbol, amount)
    elif data == "paper_portfolio":
        await _handle_paper_portfolio(update, context)
    elif data == "paper_history":
        await _handle_paper_history(update, context)
    elif data == "paper_leaderboard":
        await _handle_paper_leaderboard(update, context)
    elif data == "paper_reset":
        await _handle_paper_reset(update, context)
    else:
        # Default response for unhandled callbacks
        await query.edit_message_text(f"🔧 Feature '{data}' is coming soon! Stay tuned for updates.")

async def _handle_portfolio_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle portfolio-related callbacks"""
    user_id = update.effective_user.id

    try:
        if progressive_keyboards:
            keyboard = await progressive_keyboards.get_portfolio_keyboard(user_id)
        else:
            keyboard = None

        if data == "portfolio_basic":
            await _send_basic_portfolio(update, context, keyboard)
        elif data == "portfolio_premium":
            await _send_premium_portfolio(update, context, keyboard)
        else:
            await _send_portfolio_overview(update, context)
    except Exception as e:
        logger.error(f"❌ Error handling portfolio callback: {e}")
        await update.callback_query.edit_message_text("❌ Could not load portfolio data.")

async def _handle_signals_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle signals-related callbacks"""
    user_id = update.effective_user.id

    try:
        if data == "signals_basic":
            await _send_basic_signals(update, context)
        elif data == "premium_signals":
            await _send_premium_signals(update, context)
        else:
            await _send_signals_dashboard(update, context)
    except Exception as e:
        logger.error(f"❌ Error handling signals callback: {e}")
        await update.callback_query.edit_message_text("❌ Could not load signals data.")

async def _handle_market_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle market-related callbacks"""
    try:
        if data == "market_basic":
            await _send_basic_market_data(update, context)
        elif data == "market_scanner":
            await _send_market_scanner(update, context)
        else:
            await _send_market_data(update, context)
    except Exception as e:
        logger.error(f"❌ Error handling market callback: {e}")
        await update.callback_query.edit_message_text("❌ Could not load market data.")

async def _handle_settings_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle settings-related callbacks"""
    try:
        if data == "settings_basic":
            await _send_basic_settings(update, context)
        elif data == "settings_premium":
            await _send_premium_settings(update, context)
        else:
            await _send_account_settings(update, context)
    except Exception as e:
        logger.error(f"❌ Error handling settings callback: {e}")
        await update.callback_query.edit_message_text("❌ Could not load settings.")

async def _send_basic_portfolio(update: Update, context: ContextTypes.DEFAULT_TYPE, keyboard=None):
    """Send basic portfolio view for free users"""
    text = """
📊 **BASIS PORTFOLIO** 📊

🆓 **Gratis Features:**
• Basis balans overzicht
• Simpele performance tracking
• Basis marktdata

💎 **Upgrade naar Premium voor:**
• Geavanceerde analytics
• Real-time tracking
• Risk management tools
• Automatische trading
    """

    if not keyboard:
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")],
            [InlineKeyboardButton("🔙 Hoofdmenu", callback_data="main_menu")]
        ])

    await update.callback_query.edit_message_text(
        text,
        reply_markup=keyboard,
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_basic_signals(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send basic signals for free users"""
    text = """
📈 **BASIS SIGNALEN** 📈

🆓 **Gratis Signalen:**
• Dagelijkse markt updates
• Basis trend analyses
• Algemene markt sentiment

💎 **Premium Signalen:**
• Real-time AI signalen
• Hoge nauwkeurigheid
• Automatische trading
• Persoonlijke strategieën

Upgrade voor toegang tot premium signalen!
    """

    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")],
        [InlineKeyboardButton("🔙 Hoofdmenu", callback_data="main_menu")]
    ])

    await update.callback_query.edit_message_text(
        text,
        reply_markup=keyboard,
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_basic_market_data(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send basic market data for free users"""
    text = """
📈 **BASIS MARKTDATA** 📈

🆓 **Beschikbare Data:**
• Top cryptocurrency prijzen
• Basis markt trends
• Dagelijkse volume data
• Algemene markt sentiment

💎 **Premium Market Scanner:**
• Real-time markt scanning
• Geavanceerde technische analyse
• Custom alerts en meldingen
• Multi-timeframe analyses

Upgrade voor volledige markt toegang!
    """

    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")],
        [InlineKeyboardButton("🔙 Hoofdmenu", callback_data="main_menu")]
    ])

    await update.callback_query.edit_message_text(
        text,
        reply_markup=keyboard,
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_basic_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send basic settings for free users"""
    text = """
⚙️ **BASIS INSTELLINGEN** ⚙️

🆓 **Beschikbare Instellingen:**
• Taal voorkeur
• Basis meldingen
• Account informatie
• Help en support

💎 **Premium Instellingen:**
• Geavanceerde trading parameters
• Risk management instellingen
• API key configuratie
• Automatische trading opties

Upgrade voor volledige controle!
    """

    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("🌐 Taal", callback_data="language_settings")],
        [InlineKeyboardButton("🔔 Meldingen", callback_data="notification_settings")],
        [InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")],
        [InlineKeyboardButton("🔙 Hoofdmenu", callback_data="main_menu")]
    ])

    await update.callback_query.edit_message_text(
        text,
        reply_markup=keyboard,
        parse_mode=ParseMode.MARKDOWN
    )

async def message_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle text messages"""
    text = update.message.text
    user_id = update.effective_user.id
    state = context.user_data.get('state')

    # Handle permanent keyboard buttons
    if text == "📋 Menu":
        await _send_main_menu_message(update, context)
        return
    elif text == "📊 Trading":
        await _send_trading_dashboard_message(update, context)
        return
    elif text == "💰 Portfolio":
        await _send_portfolio_overview(update, context)
        return
    elif text == "🎯 Signalen":
        await _send_signals_dashboard(update, context)
        return
    elif text == "💬 Community":
        await _send_community_chat_message(update, context)
        return
    elif text == "📈 Market":
        await _send_market_data_message(update, context)
        return
    elif text == "🤖 AI Assistant":
        await _send_ai_assistant_message(update, context)
        return
    elif text == "⚙️ Settings":
        await _send_account_settings(update, context)
        return
    elif text == "🆘 Help":
        await help_handler(update, context)
        return

    # Process message based on user state
    if state == 'waiting_api_key':
        await _process_api_key_input(update, context, text)
    elif state == 'waiting_api_secret':
        await _process_api_secret_input(update, context, text)
    elif state == 'waiting_api_passphrase':
        await _process_api_passphrase_input(update, context, text)
    elif state == 'waiting_trade_amount':
        await _process_trade_amount_input(update, context, text)
    elif state == 'waiting_broadcast_message':
        await _process_broadcast_message(update, context, text)
    else:
        # Default response for unhandled messages
        await update.message.reply_text(
            "🤖 Gebruik de knoppen hieronder of typ een commando zoals /help voor meer opties.",
            parse_mode=ParseMode.MARKDOWN
        )

# Admin decorator
def admin_required(func):
    """Decorator to check if user is admin"""
    async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        user_id = update.effective_user.id
        is_admin = await bot_manager.user_manager.is_admin(user_id)

        if is_admin:
            return await func(update, context, *args, **kwargs)
        else:
            await update.message.reply_text(
                "❌ You don't have permission to perform this action."
            )

    return wrapper

async def _send_portfolio_overview(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send portfolio overview"""
    user_id = update.effective_user.id

    try:
        user_stats = await bot_manager.user_manager.get_user_stats(user_id)

        # Get portfolio data from trading engine
        portfolio_value = await bot_manager.trading_engine.get_portfolio_value(user_id)
        current_positions = user_stats.get('open_positions', 0)

        text = f"""
📊 **PORTFOLIO OVERVIEW** 📊

💰 **Current Status:**
├─ 💵 Total Value: {format_currency(portfolio_value)}
├─ 📈 Open Positions: {current_positions}
├─ 💸 Today's PnL: {format_currency(user_stats.get('daily_pnl', 0))}
├─ 🎯 Total PnL: {format_currency(user_stats.get('total_pnl', 0))}
└─ 📊 Win Rate: {format_percentage(user_stats.get('win_rate', 0))}

📈 **Performance:**
├─ 🔢 Total Trades: {user_stats.get('total_trades', 0)}
├─ ✅ Winning Trades: {user_stats.get('winning_trades', 0)}
├─ 💰 Total Volume: {format_currency(user_stats.get('total_volume', 0))}
└─ 📅 Member Since: {user_stats.get('member_since', 'Unknown')}

**Portfolio Actions:**
        """

        from .keyboards import get_portfolio_keyboard

        await update.message.reply_text(
            text,
            reply_markup=get_portfolio_keyboard(),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error sending portfolio overview: {e}")
        await update.message.reply_text("❌ Could not load portfolio data.")

async def _send_signals_dashboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send trading signals dashboard"""
    user_id = update.effective_user.id

    try:
        is_premium = await bot_manager.user_manager.is_premium(user_id)

        # Get latest signals
        signals = await _get_latest_signals(limit=5)

        text = f"""
📈 **TRADING SIGNALS** 📈

🔥 **Live Market Signals:**

"""

        for i, signal in enumerate(signals, 1):
            trend_emoji = "🚀" if signal['trend'] == 'bullish' else "📉"
            text += f"{i}. {trend_emoji} **{signal['symbol']}** - {signal['action']}\n"
            text += f"   💰 Price: ${signal['price']} | 🎯 Confidence: {signal['confidence']}%\n\n"

        if not is_premium:
            text += """
💎 **Premium Signals Available:**
├─ 🤖 AI-Powered Analysis
├─ 📊 Multi-Timeframe Signals
├─ 🎯 Custom Strategy Alerts
└─ ⚡ Real-time Notifications

**Upgrade for full access!**
            """

        keyboard = [
            [InlineKeyboardButton("🔄 Refresh Signals", callback_data="refresh_signals")],
            [InlineKeyboardButton("⚙️ Signal Settings", callback_data="signal_settings")],
            [InlineKeyboardButton("💎 Upgrade to Premium", callback_data="upgrade_vip")] if not is_premium else [],
            [InlineKeyboardButton("🔙 Back", callback_data="user_main")]
        ]

        # Remove empty lists
        keyboard = [row for row in keyboard if row]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error sending signals dashboard: {e}")
        await update.message.reply_text("❌ Could not load signals data.")

async def _send_community_chat(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send community chat interface - redirect to community bot"""
    text = """
💬 **PRACHTIGE TRADING COMMUNITY** 💬

🚀 **Welkom bij onze exclusieve trading community!**

🌟 **Wat je kunt doen in de community:**
├─ 📸 Deel screenshots van je trades
├─ 💰 Post je trading resultaten
├─ 🏆 Bekijk de weekly leaderboard
├─ 💬 Chat met succesvolle traders
├─ 🎯 Leer van de beste strategieën
└─ 🚀 Inspireer anderen met je succes

📊 **Community Features:**
✅ **Photo Sharing** - Deel je trade screenshots
✅ **Profit Tracking** - Automatische profit detectie
✅ **Weekly Leaderboard** - Competitie met andere traders
✅ **Achievement System** - Verdien badges en erkenning
✅ **Social Trading** - Leer van top performers

**Klik hieronder om naar de community bot te gaan:**
    """

    keyboard = [
        [InlineKeyboardButton("💬 Open Community Chat", url="https://t.me/Innovars_chatbot")],
        [InlineKeyboardButton("🔙 Terug naar Main Menu", callback_data="user_main")]
    ]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _toggle_auto_trading(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toggle auto trading for user"""
    user_id = update.effective_user.id

    try:
        user_data = await bot_manager.user_manager.get_user(user_id)
        if not user_data:
            if update.callback_query:
                await update.callback_query.edit_message_text("❌ User not found.")
            else:
                await update.message.reply_text("❌ User not found.")
            return

        # Check if user has trading enabled (default to False if not set)
        trading_enabled = getattr(user_data, 'trading_enabled', False) if hasattr(user_data, 'trading_enabled') else user_data.get('trading_enabled', False)

        if not trading_enabled:
            text = "⚠️ Trading is not enabled. Configure API keys first using /register"
            if update.callback_query:
                await update.callback_query.edit_message_text(text)
            else:
                await update.message.reply_text(text)
            return

        # Get current auto trading status (default to False if not set)
        current_auto_trading = getattr(user_data, 'auto_trading', False) if hasattr(user_data, 'auto_trading') else user_data.get('auto_trading', False)

        # Toggle auto trading
        new_auto_trading = not current_auto_trading

        await bot_manager.user_manager.update_trading_preferences(user_id, {
            'auto_trading': new_auto_trading
        })

        status = "AAN" if new_auto_trading else "UIT"
        emoji = "✅" if new_auto_trading else "❌"

        # Get user attributes safely
        default_amount = getattr(user_data, 'default_trading_amount', 100) if hasattr(user_data, 'default_trading_amount') else user_data.get('default_trading_amount', 100)
        risk_level = getattr(user_data, 'default_risk_level', 'medium') if hasattr(user_data, 'default_risk_level') else user_data.get('default_risk_level', 'medium')
        strategies = getattr(user_data, 'enabled_strategies', []) if hasattr(user_data, 'enabled_strategies') else user_data.get('enabled_strategies', [])

        text = f"""
{emoji} **Auto Trading {status}**

{'🚀 Auto trading is nu actief! De bot zal automatisch trades uitvoeren volgens je geselecteerde strategieën.' if new_auto_trading else '🛑 Auto trading is uitgeschakeld. Je kunt nog steeds signalen ontvangen en handmatig handelen.'}

**Huidige Instellingen:**
├─ ⚡ Auto Trading: {status}
├─ 💰 Standaard Bedrag: ${default_amount}
├─ ⚠️ Risico Level: {str(risk_level).title()}
└─ 🎯 Actieve Strategieën: {len(strategies) if isinstance(strategies, list) else 0}

**Snelle Acties:**
        """

        keyboard = [
            [InlineKeyboardButton("⚙️ Trading Settings", callback_data="trading_settings")],
            [InlineKeyboardButton("🎯 Manage Strategies", callback_data="manage_strategies")],
            [InlineKeyboardButton("📊 View Performance", callback_data="user_portfolio")],
            [InlineKeyboardButton("🔙 Back", callback_data="user_main")]
        ]

        if update.callback_query:
            await update.callback_query.edit_message_text(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )

    except Exception as e:
        logger.error(f"❌ Error toggling auto trading: {e}")
        error_text = "❌ Could not toggle auto trading."
        if update.callback_query:
            await update.callback_query.edit_message_text(error_text)
        else:
            await update.message.reply_text(error_text)

async def _send_account_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send account settings interface"""
    user_id = update.effective_user.id

    try:
        user = await bot_manager.user_manager.get_user(user_id)
        is_premium = await bot_manager.user_manager.is_premium(user_id)
        is_vip = await bot_manager.user_manager.is_vip(user_id)

        premium_status = "👑 VIP" if is_vip else "💎 Premium" if is_premium else "🆓 Free"

        text = f"""
⚙️ **ACCOUNT SETTINGS** ⚙️

👤 **Profile:**
├─ 📛 Name: {user.first_name} {user.last_name or ''}
├─ 🆔 User ID: {user.user_id}
├─ 🎖️ Status: {premium_status}
├─ 🌐 Language: {user.language.upper()}
└─ 📅 Member Since: {user.created_at.strftime('%Y-%m-%d')}

🔧 **Trading Settings:**
├─ 🔑 API Keys: {'✅' if user.api_keys_configured else '❌'} Configured
├─ 💹 Trading: {'✅' if user.trading_enabled else '❌'} Enabled
├─ ⚡ Auto Trading: {'✅' if user.auto_trading else '❌'}
├─ 💰 Default Amount: ${user.default_trading_amount}
└─ ⚠️ Risk Level: {user.default_risk_level.title()}

**Account Actions:**
        """

        keyboard = [
            [InlineKeyboardButton("🔑 Manage API Keys", callback_data="manage_api_keys")],
            [InlineKeyboardButton("⚙️ Trading Preferences", callback_data="trading_preferences")],
            [InlineKeyboardButton("🌐 Language Settings", callback_data="language_settings")],
            [InlineKeyboardButton("💎 Upgrade Account", callback_data="upgrade_vip")] if not is_premium else [],
            [InlineKeyboardButton("🗑️ Delete Account", callback_data="delete_account")],
            [InlineKeyboardButton("🔙 Back", callback_data="user_main")]
        ]

        # Remove empty lists
        keyboard = [row for row in keyboard if row]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error sending account settings: {e}")
        await update.message.reply_text("❌ Could not load account settings.")

# =================================================================================
# 🔐 INPUT PROCESSING FUNCTIONS
# =================================================================================

async def _process_api_key_input(update: Update, context: ContextTypes.DEFAULT_TYPE, api_key: str):
    """Process API key input"""
    user_id = update.effective_user.id

    # Store API key temporarily
    context.user_data['temp_api_key'] = api_key
    context.user_data['state'] = 'waiting_api_secret'

    await update.message.reply_text(
        "🔑 API Key received!\n\n"
        "Now please enter your **API Secret**:",
        parse_mode=ParseMode.MARKDOWN
    )

async def _process_api_secret_input(update: Update, context: ContextTypes.DEFAULT_TYPE, api_secret: str):
    """Process API secret input"""
    user_id = update.effective_user.id
    exchange = context.user_data.get('exchange', 'kucoin')

    context.user_data['temp_api_secret'] = api_secret

    if exchange.lower() == 'kucoin':
        context.user_data['state'] = 'waiting_api_passphrase'
        await update.message.reply_text(
            "🔑 API Secret received!\n\n"
            "Finally, please enter your **API Passphrase** (KuCoin only):",
            parse_mode=ParseMode.MARKDOWN
        )
    else:
        # Binance doesn't need passphrase
        await _finalize_api_setup(update, context, None)

async def _process_api_passphrase_input(update: Update, context: ContextTypes.DEFAULT_TYPE, passphrase: str):
    """Process API passphrase input"""
    await _finalize_api_setup(update, context, passphrase)

# =================================================================================
# 🎯 PERMANENT KEYBOARD MESSAGE HANDLERS
# =================================================================================

async def _send_main_menu_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send main menu via message (for permanent keyboard)"""
    user = update.effective_user
    from .keyboards import get_main_keyboard

    text = f"""
🚀 **PRACHTIGE TRADING BOT v2.0** 🚀

👋 Welcome back, {user.first_name}!

🎯 **Your Trading Command Center:**
• 📊 **Trading Hub** - Execute trades & manage positions
• 💰 **Portfolio** - Track performance & analytics
• 🎯 **Live Signals** - Real-time trading opportunities
• 📈 **Market Data** - Prices, trends & analysis
• 🤖 **AI Assistant** - Smart trading advice
• 📚 **Learning** - Improve your trading skills

✨ **Choose an option to continue:**
    """

    await update.message.reply_text(
        text,
        reply_markup=get_main_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_trading_dashboard_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send trading dashboard via message"""
    from .keyboards import get_trading_keyboard

    text = """
📊 **TRADING HUB** 📊

🚀 **Your Professional Trading Center**

💹 **Quick Actions:**
• 🛒 **Quick Buy/Sell** - Instant market orders
• 📊 **Advanced Orders** - Limit, Stop Loss, Take Profit
• 📋 **Position Management** - Monitor open trades
• 📜 **Trade History** - Review past performance

⚡ **Smart Features:**
• 🔄 **Real-time Prices** - Live market updates
• ⚙️ **Trading Settings** - Customize your experience
• 🎯 **Risk Management** - Protect your capital

**Select your trading action:**
    """

    await update.message.reply_text(
        text,
        reply_markup=get_trading_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_community_chat_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send community chat interface via message"""
    text = """
💬 **PRACHTIGE TRADING COMMUNITY** 💬

🚀 **Welkom bij onze exclusieve trading community!**

🌟 **Wat je kunt doen in de community:**
├─ 📸 Deel screenshots van je trades
├─ 💰 Post je trading resultaten
├─ 🏆 Bekijk de weekly leaderboard
├─ 💬 Chat met succesvolle traders
├─ 🎯 Leer van de beste strategieën
└─ 🚀 Inspireer anderen met je succes

📊 **Community Features:**
✅ **Photo Sharing** - Deel je trade screenshots
✅ **Profit Tracking** - Automatische profit detectie
✅ **Weekly Leaderboard** - Competitie met andere traders
✅ **Achievement System** - Verdien badges en erkenning
✅ **Social Trading** - Leer van top performers

**Klik hieronder om naar de community bot te gaan:**
    """

    keyboard = [
        [InlineKeyboardButton("💬 Open Community Chat", url="https://t.me/Innovars_chatbot")],
        [InlineKeyboardButton("🔙 Terug naar Main Menu", callback_data="user_main")]
    ]

    await update.message.reply_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_market_data_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send market data via message"""
    from .keyboards import get_market_data_keyboard

    text = """
📈 **LIVE MARKET DATA** 📈

🌍 **Global Crypto Markets - Real Time**

🔥 **Market Highlights:**
• ₿ **Bitcoin**: $44,250 (+2.3% 24h)
• ⟠ **Ethereum**: $2,680 (+1.8% 24h)
• 📊 **Total Market Cap**: $1.68T
• 😱 **Fear & Greed Index**: 72 (Greed)

📊 **Market Analysis:**
• 🚀 **Trending**: Strong bullish momentum
• 📈 **Volume**: $89.2B (24h)
• 🎯 **Support**: BTC $42,000 | ETH $2,500
• ⚠️ **Resistance**: BTC $46,000 | ETH $2,800

**Select market data to explore:**
    """

    await update.message.reply_text(
        text,
        reply_markup=get_market_data_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_ai_assistant_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send AI assistant via message"""
    from .keyboards import get_ai_assistant_keyboard

    text = """
🤖 **AI TRADING ASSISTANT** 🤖

🧠 **Your Personal Trading AI**

💡 **AI Capabilities:**
• 📊 **Market Analysis** - Real-time insights
• 🎯 **Trading Advice** - Personalized recommendations
• 📈 **Strategy Help** - Optimize your approach
• 🔮 **Price Predictions** - AI-powered forecasts
• ⚠️ **Risk Assessment** - Protect your capital

🚀 **Smart Features:**
• 💬 **Natural Language** - Chat like with a human
• 📚 **Educational Q&A** - Learn while you trade
• 🎓 **Personalized Learning** - Adapted to your level

**Choose your AI assistance:**
    """

    await update.message.reply_text(
        text,
        reply_markup=get_ai_assistant_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

# =================================================================================
# 🚀 BROADCAST SYSTEM HANDLERS
# =================================================================================

async def _send_welcome_broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send welcome broadcast message to new user"""
    user = update.effective_user

    text = f"""
🎉 **WELKOM BIJ PRACHTIGE TRADING BOT, {user.first_name}!** 🎉

🚀 **Je bent nu geregistreerd voor:**
✅ **Dagelijkse Marktanalyse** - Elke ochtend om 09:00
✅ **Live Trading Signalen** - Real-time buy/sell aanbevelingen
✅ **Markt Alerts** - Belangrijke prijsbewegingen
✅ **Interactieve Trading** - Direct traden via bot berichten

📊 **Wat je kunt verwachten:**
├─ 💰 Actuele crypto prijzen (BTC, ETH, SOL, etc.)
├─ 📈 Technische analyse met RSI, support/resistance
├─ 🎯 Buy/Sell aanbevelingen van onze AI
├─ 🟢🔴 **Directe trading knoppen** in elk bericht
└─ 📱 Eenvoudige order plaatsing via de bot

🔥 **EERSTE MARKTANALYSE KOMT ERAAN!**
Je ontvangt binnen 24 uur je eerste dagelijkse marktanalyse met interactieve trading knoppen.

**Veel succes met traden!** 🚀
    """

    keyboard = [
        [InlineKeyboardButton("📊 Ontvang Nu Marktanalyse", callback_data="get_analysis_now")],
        [InlineKeyboardButton("⚙️ Broadcast Instellingen", callback_data="broadcast_settings")]
    ]

    await update.message.reply_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _handle_trading_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle trading button callbacks"""
    if not broadcast_system:
        await update.callback_query.edit_message_text("❌ Broadcast system niet beschikbaar")
        return

    # Parse callback data: trade_buy_BTC or trade_sell_ETH
    parts = data.split("_")
    action = parts[1]  # buy or sell
    symbol = parts[2]  # BTC, ETH, etc.

    user_id = update.effective_user.id

    # Create trading session
    session_id = broadcast_system.create_trading_session(user_id, action, symbol)

    # Get amount input message
    text, keyboard = broadcast_system.get_trading_amount_message(session_id)

    await update.callback_query.edit_message_text(
        text,
        reply_markup=keyboard,
        parse_mode=ParseMode.MARKDOWN
    )

async def _handle_amount_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle amount selection callbacks"""
    if not broadcast_system:
        await update.callback_query.edit_message_text("❌ Broadcast system niet beschikbaar")
        return

    # Parse callback data: amount_sessionid_100
    parts = data.split("_")
    session_id = "_".join(parts[1:-1])
    amount = float(parts[-1])

    # Update session with amount
    if session_id in broadcast_system.trading_sessions:
        session = broadcast_system.trading_sessions[session_id]
        session["amount"] = amount
        session["total_value"] = amount
        session["status"] = "confirmation"
        broadcast_system.save_trading_sessions()

        # Get confirmation message
        text, keyboard = broadcast_system.get_trading_confirmation_message(session_id)

        await update.callback_query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
    else:
        await update.callback_query.edit_message_text("❌ Trading sessie niet gevonden")

async def _handle_confirm_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle order confirmation callbacks"""
    if not broadcast_system:
        await update.callback_query.edit_message_text("❌ Broadcast system niet beschikbaar")
        return

    # Parse callback data: confirm_sessionid
    session_id = data.replace("confirm_", "")

    if session_id in broadcast_system.trading_sessions:
        session = broadcast_system.trading_sessions[session_id]

        # Execute trade
        success, result = await broadcast_system.execute_trade(session_id)

        if success:
            action = session["action"].upper()
            symbol = session["symbol"]
            amount = session["amount"]
            order_id = result

            text = f"""
✅ **ORDER SUCCESVOL UITGEVOERD!** ✅

🎯 **Order Details:**
├─ 📊 Pair: {session["pair"]}
├─ 🎯 Action: {action}
├─ 💰 Amount: ${amount:,.2f}
├─ 💵 Price: ${session["price"]:,.2f}
├─ 🆔 Order ID: {order_id}
└─ ⏰ Executed: {datetime.now().strftime("%H:%M:%S")}

🚀 **Je trade is succesvol uitgevoerd!**
Check je portfolio voor de update.

**Veel succes met je investering!** 💰
            """

            keyboard = [
                [InlineKeyboardButton("📊 Bekijk Portfolio", callback_data="portfolio_overview")],
                [InlineKeyboardButton("📈 Nieuwe Trade", callback_data="get_analysis_now")]
            ]
        else:
            text = f"""
❌ **ORDER MISLUKT** ❌

🚫 **Reden:** {result}

💡 **Mogelijke oorzaken:**
├─ Onvoldoende saldo
├─ Markt gesloten
├─ Technische storing
└─ Ongeldige parameters

**Probeer het later opnieuw of neem contact op met support.**
            """

            keyboard = [
                [InlineKeyboardButton("🔄 Probeer Opnieuw", callback_data="get_analysis_now")],
                [InlineKeyboardButton("🆘 Contact Support", callback_data="support_contact")]
            ]

        await update.callback_query.edit_message_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )
    else:
        await update.callback_query.edit_message_text("❌ Trading sessie niet gevonden")

async def _handle_cancel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle order cancellation callbacks"""
    session_id = data.replace("cancel_", "")

    text = """
❌ **ORDER GEANNULEERD** ❌

Je trading order is geannuleerd.
Geen zorgen, je kunt altijd een nieuwe order plaatsen!

**Wil je een nieuwe trade starten?**
    """

    keyboard = [
        [InlineKeyboardButton("📈 Nieuwe Trade", callback_data="get_analysis_now")],
        [InlineKeyboardButton("🔙 Terug naar Menu", callback_data="user_main")]
    ]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _handle_detailed_analysis(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle detailed analysis request"""
    if not broadcast_system:
        await update.callback_query.edit_message_text("❌ Broadcast system niet beschikbaar")
        return

    # Send current market analysis
    text, keyboard = broadcast_system.get_daily_analysis_message()

    await update.callback_query.edit_message_text(
        text,
        reply_markup=keyboard,
        parse_mode=ParseMode.MARKDOWN
    )

async def _handle_broadcast_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle broadcast settings"""
    user_id = str(update.effective_user.id)

    if broadcast_system and user_id in broadcast_system.subscribers["users"]:
        user_prefs = broadcast_system.subscribers["users"][user_id]["preferences"]

        daily_status = "✅" if user_prefs["daily_analysis"] else "❌"
        signals_status = "✅" if user_prefs["trading_signals"] else "❌"
        alerts_status = "✅" if user_prefs["market_alerts"] else "❌"

        text = f"""
⚙️ **BROADCAST INSTELLINGEN** ⚙️

📊 **Huidige Instellingen:**
├─ {daily_status} **Dagelijkse Marktanalyse**
├─ {signals_status} **Trading Signalen**
└─ {alerts_status} **Markt Alerts**

🎯 **Preferred Trading Pairs:**
{', '.join(user_prefs["preferred_pairs"])}

**Klik hieronder om instellingen te wijzigen:**
        """

        keyboard = [
            [
                InlineKeyboardButton(f"{daily_status} Daily Analysis", callback_data=f"toggle_daily_{user_id}"),
                InlineKeyboardButton(f"{signals_status} Signals", callback_data=f"toggle_signals_{user_id}")
            ],
            [
                InlineKeyboardButton(f"{alerts_status} Alerts", callback_data=f"toggle_alerts_{user_id}"),
                InlineKeyboardButton("🎯 Edit Pairs", callback_data=f"edit_pairs_{user_id}")
            ],
            [InlineKeyboardButton("🔙 Terug", callback_data="get_analysis_now")]
        ]
    else:
        text = "❌ Gebruiker niet gevonden in broadcast systeem"
        keyboard = [[InlineKeyboardButton("🔙 Terug", callback_data="user_main")]]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

# =================================================================================
# 🆕 NIEUWE MENU HANDLERS
# =================================================================================

async def _send_main_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send main menu"""
    user = update.effective_user
    from .keyboards import get_main_keyboard

    text = f"""
🚀 **PRACHTIGE TRADING BOT v2.0** 🚀

👋 Welcome back, {user.first_name}!

🎯 **Your Trading Command Center:**
• 📊 **Trading Hub** - Execute trades & manage positions
• 💰 **Portfolio** - Track performance & analytics
• 🎯 **Live Signals** - Real-time trading opportunities
• 📈 **Market Data** - Prices, trends & analysis
• 🤖 **AI Assistant** - Smart trading advice
• 📚 **Learning** - Improve your trading skills

✨ **Choose an option to continue:**
    """

    await update.callback_query.edit_message_text(
        text,
        reply_markup=get_main_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_trading_dashboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send trading dashboard"""
    from .keyboards import get_trading_keyboard

    text = """
📊 **TRADING HUB** 📊

🚀 **Your Professional Trading Center**

💹 **Quick Actions:**
• 🛒 **Quick Buy/Sell** - Instant market orders
• 📊 **Advanced Orders** - Limit, Stop Loss, Take Profit
• 📋 **Position Management** - Monitor open trades
• 📜 **Trade History** - Review past performance

⚡ **Smart Features:**
• 🔄 **Real-time Prices** - Live market updates
• ⚙️ **Trading Settings** - Customize your experience
• 🎯 **Risk Management** - Protect your capital

**Select your trading action:**
    """

    await update.callback_query.edit_message_text(
        text,
        reply_markup=get_trading_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_market_data(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send market data dashboard"""
    from .keyboards import get_market_data_keyboard

    text = """
📈 **LIVE MARKET DATA** 📈

🌍 **Global Crypto Markets - Real Time**

🔥 **Market Highlights:**
• ₿ **Bitcoin**: $44,250 (+2.3% 24h)
• ⟠ **Ethereum**: $2,680 (+1.8% 24h)
• 📊 **Total Market Cap**: $1.68T
• 😱 **Fear & Greed Index**: 72 (Greed)

📊 **Market Overview:**
• 🔥 **Top Gainers**: AVAX (+12%), SOL (+8%)
• 📉 **Top Losers**: ADA (-3%), DOT (-2%)
• 📈 **Trending**: AI tokens, DeFi protocols
• 💰 **24h Volume**: $89.2B

**Explore market data:**
    """

    await update.callback_query.edit_message_text(
        text,
        reply_markup=get_market_data_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_ai_assistant(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send AI assistant dashboard"""
    from .keyboards import get_ai_assistant_keyboard

    text = """
🤖 **AI TRADING ASSISTANT** 🤖

🧠 **Your Personal Trading AI**

✨ **AI Capabilities:**
• 💡 **Smart Trading Advice** - Personalized recommendations
• 📊 **Market Analysis** - Deep technical & fundamental analysis
• 🎯 **Strategy Optimization** - Improve your trading approach
• 🔮 **Price Predictions** - AI-powered forecasting
• ⚠️ **Risk Assessment** - Intelligent risk management

🚀 **Powered by Advanced AI:**
• 📈 Technical Analysis AI
• 📰 Sentiment Analysis
• 🔍 Pattern Recognition
• 📊 Multi-timeframe Analysis

**How can I help you today?**
    """

    await update.callback_query.edit_message_text(
        text,
        reply_markup=get_ai_assistant_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_learning_center(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send learning center"""
    from .keyboards import get_learning_center_keyboard

    text = """
📚 **TRADING LEARNING CENTER** 📚

🎓 **Master the Art of Trading**

📖 **Educational Content:**
• 🎓 **Trading Basics** - Start your journey
• 📈 **Technical Analysis** - Chart patterns & indicators
• 💰 **Risk Management** - Protect your capital
• 🎯 **Trading Strategies** - Proven approaches
• 🤖 **Bot Usage** - Maximize automation
• 📊 **Market Psychology** - Master emotions

🏆 **Interactive Learning:**
• 🧠 **Trading Quiz** - Test your knowledge
• 📖 **Glossary** - Trading terminology
• 💡 **Tips & Tricks** - Pro trader secrets
• 📺 **Video Tutorials** - Visual learning

**Start learning today:**
    """

    await update.callback_query.edit_message_text(
        text,
        reply_markup=get_learning_center_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_support_center(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send support center"""
    from .keyboards import get_support_center_keyboard

    text = """
🆘 **SUPPORT CENTER** 🆘

👨‍💻 **24/7 Professional Support**

🔧 **Get Help:**
• ❓ **FAQ** - Common questions & answers
• 📞 **Contact Support** - Direct assistance
• 🐛 **Report Bug** - Help us improve
• 💡 **Feature Request** - Suggest improvements
• 📋 **User Guide** - Complete documentation
• 🔧 **Troubleshooting** - Fix common issues

⚡ **Quick Support:**
• 💬 **Live Chat** - Instant help
• 📧 **Email Support** - <EMAIL>
• 📱 **Telegram Group** - Community support
• 🎥 **Video Guides** - Visual tutorials

**How can we help you?**
    """

    await update.callback_query.edit_message_text(
        text,
        reply_markup=get_support_center_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _send_about_bot(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send about bot information"""
    text = """
ℹ️ **ABOUT PRACHTIGE TRADING BOT** ℹ️

🚀 **Version 2.0.0** - Professional Trading Suite

👨‍💻 **Developed by Innovars Lab**
🌟 **Trusted by 10,000+ Traders Worldwide**

🎯 **Key Features:**
• 🤖 **AI-Powered Trading** - Smart automation
• 📊 **Multi-Exchange Support** - KuCoin, Binance & more
• 📈 **Advanced Strategies** - Proven algorithms
• 💎 **Premium Features** - VIP trading tools
• 🔐 **Enterprise Security** - Bank-level protection
• 📱 **Mobile-First Design** - Trade anywhere

🏆 **Awards & Recognition:**
• 🥇 Best Crypto Trading Bot 2024
• 🌟 5-star rating from 95% of users
• 🔒 SOC 2 Type II Certified
• 💎 Featured in CoinDesk, CoinTelegraph

📞 **Contact:**
• 🌐 Website: prachtigebot.com
• 📧 Email: <EMAIL>
• 💬 Telegram: @PrachtigeBotSupport

**Thank you for choosing Prachtige Trading Bot!**
    """

    keyboard = [[InlineKeyboardButton("🔙 Back to Menu", callback_data="user_main")]]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _finalize_api_setup(update: Update, context: ContextTypes.DEFAULT_TYPE, passphrase: Optional[str]):
    """Finalize API key setup"""
    user_id = update.effective_user.id
    exchange = context.user_data.get('exchange', 'kucoin')

    try:
        api_data = {
            'api_key': context.user_data.get('temp_api_key'),
            'api_secret': context.user_data.get('temp_api_secret'),
        }

        if passphrase:
            api_data['passphrase'] = passphrase

        # Store encrypted API keys
        success = await bot_manager.user_manager.store_api_keys(
            user_id, exchange, api_data
        )

        if success:
            text = f"""
✅ **API Keys Configured Successfully!**

🎉 Your {exchange.title()} account is now connected!

🚀 **What's Next:**
├─ 💹 Enable Trading Strategies
├─ 💰 Set Trading Amount
├─ ⚡ Configure Auto Trading
└─ 🎯 Start Trading!

**Your trading journey begins now!**
            """

            keyboard = [
                [InlineKeyboardButton("🎯 Configure Strategies", callback_data="configure_strategies")],
                [InlineKeyboardButton("💰 Set Trading Amount", callback_data="set_trading_amount")],
                [InlineKeyboardButton("📊 Go to Dashboard", callback_data="user_main")]
            ]

            await update.message.reply_text(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                "❌ Failed to save API keys. Please try again or contact support."
            )

        # Clear temporary data
        context.user_data.clear()

    except Exception as e:
        logger.error(f"❌ Error finalizing API setup: {e}")
        await update.message.reply_text(
            "❌ An error occurred while setting up your API keys. Please try again."
        )
        context.user_data.clear()

async def _process_trade_amount_input(update: Update, context: ContextTypes.DEFAULT_TYPE, amount_text: str):
    """Process trading amount input"""
    user_id = update.effective_user.id

    try:
        amount = float(amount_text.replace('$', '').replace(',', ''))

        if amount < 10:
            await update.message.reply_text(
                "⚠️ Minimum trading amount is $10. Please enter a higher amount:"
            )
            return

        if amount > 10000:
            await update.message.reply_text(
                "⚠️ Maximum trading amount is $10,000. Please enter a lower amount:"
            )
            return

        # Update user preferences
        await bot_manager.user_manager.update_trading_preferences(user_id, {
            'default_trading_amount': amount
        })

        await update.message.reply_text(
            f"✅ Trading amount set to **${amount:,.2f}**\n\n"
            "This amount will be used for each trade unless specified otherwise.",
            parse_mode=ParseMode.MARKDOWN
        )

        context.user_data.clear()

    except ValueError:
        await update.message.reply_text(
            "❌ Invalid amount format. Please enter a number (e.g., 100 or 100.50):"
        )

@admin_required
async def _process_broadcast_message(update: Update, context: ContextTypes.DEFAULT_TYPE, message: str):
    """Process broadcast message from admin"""
    user_id = update.effective_user.id

    try:
        # Get broadcast target
        target = context.user_data.get('broadcast_target', 'all')

        # Send broadcast
        sent_count, failed_count = await bot_manager.broadcast_message(message, target)

        await update.message.reply_text(
            f"📤 **Broadcast Sent!**\n\n"
            f"✅ Sent: {sent_count} users\n"
            f"❌ Failed: {failed_count} users\n"
            f"🎯 Target: {target}"
        )

        context.user_data.clear()

    except Exception as e:
        logger.error(f"❌ Error processing broadcast: {e}")
        await update.message.reply_text("❌ Broadcast failed.")
        context.user_data.clear()

# =================================================================================
# 🔧 HELPER FUNCTIONS
# =================================================================================

async def _get_latest_signals(limit: int = 5) -> List[Dict]:
    """Get latest trading signals"""
    # This would integrate with your signal generation system
    # For now, return mock data
    return [
        {
            'symbol': 'BTC/USDT',
            'action': 'Strong Buy',
            'price': 44250.00,
            'confidence': 85,
            'trend': 'bullish'
        },
        {
            'symbol': 'ETH/USDT',
            'action': 'Buy',
            'price': 2650.00,
            'confidence': 78,
            'trend': 'bullish'
        },
        {
            'symbol': 'ADA/USDT',
            'action': 'Sell',
            'price': 0.45,
            'confidence': 72,
            'trend': 'bearish'
        }
    ]

async def _send_analytics_dashboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send analytics dashboard for admins"""
    try:
        # Get system stats
        stats = await bot_manager.get_system_health()
        user_report = await bot_manager.user_manager.get_user_activity_report()
        trading_stats = await bot_manager.trading_engine.get_stats()

        text = f"""
📊 **SYSTEM ANALYTICS** 📊

👥 **User Statistics:**
├─ 📊 Total Users: {user_report.get('total_users', 0)}
├─ 🟢 Active Users: {user_report.get('active_users', 0)}
├─ 🆕 New Users (30d): {user_report.get('new_users', 0)}
├─ 💎 Premium Users: {user_report.get('premium_users', 0)}
└─ 👑 VIP Users: {user_report.get('vip_users', 0)}

💹 **Trading Statistics:**
├─ 🔢 Total Trades: {trading_stats.get('total_trades', 0)}
├─ 💰 Total Volume: {format_currency(trading_stats.get('total_volume', 0))}
├─ 📈 Total PnL: {format_currency(trading_stats.get('total_pnl', 0))}
├─ 🎯 Win Rate: {format_percentage(trading_stats.get('win_rate', 0))}
└─ 📊 Open Positions: {trading_stats.get('open_positions', 0)}

🖥️ **System Health:**
├─ 🤖 Bot Status: {stats.get('bot_status', 'unknown').title()}
├─ 💹 Trading Engine: {stats.get('trading_engine', 'unknown').title()}
├─ 🗄️ Database: {stats.get('database', 'unknown').title()}
├─ ⏱️ Uptime: {stats.get('uptime_hours', 0):.1f}h
└─ 💾 Memory: {stats.get('memory_usage', 0):.1f}MB

**System Management:**
        """

        keyboard = [
            [InlineKeyboardButton("🔄 Refresh Stats", callback_data="refresh_analytics")],
            [InlineKeyboardButton("👥 User Management", callback_data="ceo_users")],
            [InlineKeyboardButton("💹 Trading Controls", callback_data="trading_controls")],
            [InlineKeyboardButton("🔙 Back to CEO", callback_data="ceo_main")]
        ]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error sending analytics dashboard: {e}")
        await update.message.reply_text("❌ Could not load analytics data.")

# =================================================================================
# 🔄 CALLBACK HANDLERS VOOR SUBMENU'S
# =================================================================================

async def _handle_market_data_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle market data callbacks"""
    if data == "market_btc":
        text = """
₿ **BITCOIN (BTC) ANALYSIS** ₿

📊 **Current Price**: $44,250.00 (+2.3% 24h)
📈 **24h High**: $45,100.00
📉 **24h Low**: $43,200.00
💰 **Market Cap**: $865.2B
📊 **Volume**: $28.5B

🔍 **Technical Analysis**:
• 📈 **Trend**: Bullish
• 🎯 **Support**: $43,000
• 🚀 **Resistance**: $45,500
• 📊 **RSI**: 68 (Slightly Overbought)

💡 **AI Prediction**: Potential breakout above $45,500
        """
    elif data == "market_eth":
        text = """
⟠ **ETHEREUM (ETH) ANALYSIS** ⟠

📊 **Current Price**: $2,680.00 (+1.8% 24h)
📈 **24h High**: $2,720.00
📉 **24h Low**: $2,620.00
💰 **Market Cap**: $322.1B
📊 **Volume**: $15.2B

🔍 **Technical Analysis**:
• 📈 **Trend**: Bullish
• 🎯 **Support**: $2,600
• 🚀 **Resistance**: $2,750
• 📊 **RSI**: 65 (Neutral)

💡 **AI Prediction**: Consolidation before next move
        """
    elif data == "market_gainers":
        text = """
🔥 **TOP GAINERS (24H)** 🔥

1. 🚀 **AVAX**: $42.50 (+12.3%)
2. 🌟 **SOL**: $98.20 (+8.7%)
3. ⚡ **MATIC**: $0.85 (+7.2%)
4. 🔥 **LINK**: $15.40 (+6.8%)
5. 💎 **DOT**: $7.20 (+5.9%)

📊 **Market Sentiment**: Very Bullish
🎯 **Trend**: Altcoin season starting
        """
    elif data == "market_losers":
        text = """
📉 **TOP LOSERS (24H)** 📉

1. 📉 **ADA**: $0.45 (-3.2%)
2. 🔻 **XRP**: $0.58 (-2.8%)
3. 📉 **LTC**: $72.30 (-2.1%)
4. 🔻 **BCH**: $245.00 (-1.9%)
5. 📉 **ETC**: $28.50 (-1.5%)

📊 **Market Sentiment**: Mixed
⚠️ **Note**: Minor corrections in strong market
        """
    else:
        text = "🔧 This market data feature is coming soon!"

    keyboard = [[InlineKeyboardButton("🔙 Back to Market Data", callback_data="market_data")]]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _handle_ai_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle AI assistant callbacks"""
    if data == "ai_trading_advice":
        text = """
💡 **AI TRADING ADVICE** 💡

🤖 **Personalized Recommendations for You:**

📈 **Current Market Analysis:**
• **Bitcoin**: Strong bullish momentum, consider DCA strategy
• **Ethereum**: Consolidation phase, wait for breakout
• **Altcoins**: Selective buying opportunities

🎯 **Trading Suggestions:**
1. **BTC**: 60% allocation, buy on dips below $43,500
2. **ETH**: 25% allocation, wait for $2,600 support test
3. **Alts**: 15% allocation, focus on AI/DeFi tokens

⚠️ **Risk Management:**
• Set stop-loss at -5% for each position
• Take profits at +15% for swing trades
• Keep 20% cash for opportunities

💡 **AI Confidence**: 82% (High)
        """
    elif data == "ai_market_analysis":
        text = """
📊 **AI MARKET ANALYSIS** 📊

🧠 **Deep Learning Insights:**

📈 **Market Sentiment**: 72/100 (Bullish)
🔍 **Pattern Recognition**: Ascending triangle forming
📊 **Volume Analysis**: Increasing buying pressure
📰 **News Sentiment**: 68% Positive

🎯 **Key Levels to Watch:**
• **BTC**: $45,500 (breakout), $43,000 (support)
• **ETH**: $2,750 (resistance), $2,600 (support)
• **Market Cap**: $1.7T (next target)

🔮 **AI Predictions (Next 7 Days)**:
• 65% chance of continued uptrend
• 25% chance of sideways movement
• 10% chance of correction

⚡ **Trading Opportunities**: High
        """
    elif data == "ai_prediction":
        text = """
🔮 **AI PRICE PREDICTIONS** 🔮

🤖 **Advanced ML Models Forecast:**

₿ **Bitcoin (BTC)**:
• 📅 **24h**: $44,800 - $46,200
• 📅 **7d**: $46,000 - $48,500
• 📅 **30d**: $48,000 - $52,000
• 🎯 **Confidence**: 78%

⟠ **Ethereum (ETH)**:
• 📅 **24h**: $2,720 - $2,850
• 📅 **7d**: $2,800 - $3,100
• 📅 **30d**: $3,000 - $3,400
• 🎯 **Confidence**: 74%

📊 **Model Accuracy**: 76% (Last 30 days)
⚠️ **Disclaimer**: Predictions are estimates, not guarantees
        """
    else:
        text = "🤖 This AI feature is coming soon! Our team is training advanced models."

    keyboard = [[InlineKeyboardButton("🔙 Back to AI Assistant", callback_data="ai_assistant")]]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _handle_learning_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle learning center callbacks"""
    if data == "learn_basics":
        text = """
🎓 **TRADING BASICS** 🎓

📚 **Essential Concepts:**

💰 **What is Trading?**
Trading is buying and selling assets to profit from price movements.

📊 **Key Terms:**
• **Bull Market**: Rising prices
• **Bear Market**: Falling prices
• **Support**: Price floor level
• **Resistance**: Price ceiling level
• **Volume**: Amount of trading activity

🎯 **Trading Types:**
• **Day Trading**: Same-day trades
• **Swing Trading**: Days to weeks
• **Position Trading**: Weeks to months

📈 **Getting Started:**
1. Learn market basics
2. Practice with small amounts
3. Develop a strategy
4. Manage risk properly

**Next**: Technical Analysis →
        """
    elif data == "learn_technical":
        text = """
📈 **TECHNICAL ANALYSIS** 📈

🔍 **Chart Reading Fundamentals:**

📊 **Candlestick Patterns:**
• **Doji**: Indecision
• **Hammer**: Potential reversal
• **Engulfing**: Strong momentum
• **Triangle**: Consolidation

📈 **Key Indicators:**
• **RSI**: Overbought/oversold levels
• **MACD**: Trend and momentum
• **Moving Averages**: Trend direction
• **Volume**: Confirms price moves

🎯 **Support & Resistance:**
• Identify key levels
• Watch for breakouts
• Use for entry/exit points

💡 **Pro Tips:**
• Multiple timeframe analysis
• Combine indicators
• Practice pattern recognition

**Next**: Risk Management →
        """
    elif data == "learn_quiz":
        text = """
🏆 **TRADING KNOWLEDGE QUIZ** 🏆

🧠 **Test Your Skills!**

❓ **Question 1/5:**
What does RSI stand for?

A) Relative Strength Index
B) Real Stock Indicator
C) Risk Signal Indicator
D) Random System Index

📊 **Your Progress:**
• Questions Answered: 0/5
• Current Score: 0%
• Best Score: Not set

🎯 **Quiz Topics:**
• Technical Analysis
• Risk Management
• Market Psychology
• Trading Strategies

🏅 **Rewards:**
• Bronze: 60%+ (Trading Badge)
• Silver: 80%+ (Expert Badge)
• Gold: 95%+ (Master Badge)

**Ready to start?**
        """
    else:
        text = "📚 This learning module is coming soon! We're preparing comprehensive content."

    keyboard = [[InlineKeyboardButton("🔙 Back to Learning", callback_data="learning_center")]]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _handle_support_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle support center callbacks"""
    if data == "support_faq":
        text = """
❓ **FREQUENTLY ASKED QUESTIONS** ❓

🔧 **Common Questions:**

**Q: How do I start trading?**
A: Connect your exchange API keys via /register, then use the Trading Hub.

**Q: Is my money safe?**
A: We use bank-level encryption and never store withdrawal permissions.

**Q: What exchanges are supported?**
A: Currently KuCoin and Binance, with more coming soon.

**Q: How much does it cost?**
A: Basic features are free. Premium starts at $29/month.

**Q: Can I cancel anytime?**
A: Yes, cancel anytime with no penalties.

**Q: Do you offer refunds?**
A: 30-day money-back guarantee for premium subscriptions.

**Need more help?** Contact our support team!
        """
    elif data == "support_contact":
        text = """
📞 **CONTACT SUPPORT** 📞

👨‍💻 **Get Professional Help:**

📧 **Email Support:**
• General: <EMAIL>
• Technical: <EMAIL>
• Billing: <EMAIL>

💬 **Live Chat:**
• Available 24/7
• Average response: 2 minutes
• Languages: EN, NL, DE, FR

📱 **Telegram Support:**
• @PrachtigeBotSupport
• Community group: @PrachtigeTradingCommunity

📞 **Phone Support (Premium):**
• US: ******-TRADING
• EU: +31-20-TRADING
• Available: Mon-Fri 9AM-6PM

🎫 **Priority Support:**
Upgrade to Premium for faster response times!
        """
    elif data == "support_bug":
        text = """
🐛 **REPORT A BUG** 🐛

🔧 **Help Us Improve:**

📝 **Bug Report Template:**

**What happened?**
Describe the issue you encountered.

**Steps to reproduce:**
1. Step one
2. Step two
3. Step three

**Expected behavior:**
What should have happened?

**Device info:**
• Device: (iPhone/Android/Desktop)
• OS Version:
• App Version: 2.0.0

📧 **Send to:** <EMAIL>

🎁 **Bug Bounty:**
Find critical bugs and earn rewards up to $500!

**Thank you for helping us improve!**
        """
    else:
        text = "🆘 This support feature is coming soon! Contact us directly for immediate help."

    keyboard = [[InlineKeyboardButton("🔙 Back to Support", callback_data="support_center")]]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def _handle_settings_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, data: str):
    """Handle settings callbacks"""
    if data == "settings_notifications":
        text = """
🔔 **NOTIFICATION SETTINGS** 🔔

📱 **Customize Your Alerts:**

🎯 **Trading Notifications:**
• ✅ Trade Executions
• ✅ Signal Alerts
• ❌ Price Alerts
• ✅ Portfolio Updates

📊 **Market Notifications:**
• ✅ Major Market Moves
• ❌ News Updates
• ✅ Technical Signals
• ❌ Social Sentiment

⏰ **Timing Preferences:**
• Active Hours: 9AM - 9PM
• Timezone: UTC+1
• Weekend Alerts: Disabled
• Emergency Only: Enabled

🔇 **Do Not Disturb:**
• Quiet Hours: 10PM - 8AM
• Vacation Mode: Disabled

**Customize your notification preferences:**
        """
    elif data == "settings_security":
        text = """
🔐 **SECURITY SETTINGS** 🔐

🛡️ **Account Protection:**

🔑 **Authentication:**
• ✅ Two-Factor Authentication (2FA)
• ✅ Login Notifications
• ✅ Device Verification
• ❌ Biometric Login

🔒 **API Security:**
• ✅ Encrypted Storage
• ✅ Read-Only Mode Available
• ❌ Withdrawal Permissions
• ✅ IP Whitelist

📱 **Session Management:**
• Active Sessions: 2
• Last Login: 2 hours ago
• Login History: Available

⚠️ **Security Alerts:**
• Suspicious Activity: Enabled
• New Device Login: Enabled
• API Key Changes: Enabled

**Your account is secure! 🛡️**
        """
    else:
        text = "⚙️ This settings feature is coming soon! We're building comprehensive options."

    keyboard = [[InlineKeyboardButton("🔙 Back to Settings", callback_data="account_settings")]]

    await update.callback_query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

# Paper Trading Handler Functions
async def _handle_paper_trading_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle paper trading menu"""
    try:
        from handlers.paper_trading_handler import paper_trading_handler
        await paper_trading_handler.show_paper_trading_menu(update, context)
    except Exception as e:
        logger.error(f"❌ Error in paper trading menu: {e}")
        await update.callback_query.edit_message_text("❌ Could not load paper trading menu.")

async def _handle_paper_buy_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle paper trading buy menu"""
    try:
        from handlers.paper_trading_handler import paper_trading_handler
        await paper_trading_handler.show_buy_menu(update, context)
    except Exception as e:
        logger.error(f"❌ Error in paper buy menu: {e}")
        await update.callback_query.edit_message_text("❌ Could not load buy menu.")

async def _handle_paper_sell_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle paper trading sell menu"""
    try:
        from handlers.paper_trading_handler import paper_trading_handler
        await paper_trading_handler.show_sell_menu(update, context)
    except Exception as e:
        logger.error(f"❌ Error in paper sell menu: {e}")
        await update.callback_query.edit_message_text("❌ Could not load sell menu.")

async def _handle_paper_buy_crypto(update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str):
    """Handle buying specific crypto"""
    try:
        from handlers.paper_trading_handler import paper_trading_handler
        await paper_trading_handler.handle_buy_crypto(update, context, symbol)
    except Exception as e:
        logger.error(f"❌ Error in paper buy crypto: {e}")
        await update.callback_query.edit_message_text("❌ Could not process buy request.")

async def _handle_paper_sell_crypto(update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str):
    """Handle selling specific crypto"""
    try:
        from handlers.paper_trading_handler import paper_trading_handler
        await paper_trading_handler.handle_sell_crypto(update, context, symbol)
    except Exception as e:
        logger.error(f"❌ Error in paper sell crypto: {e}")
        await update.callback_query.edit_message_text("❌ Could not process sell request.")

async def _handle_paper_execute_buy(update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str, amount: float):
    """Handle executing buy order"""
    try:
        from handlers.paper_trading_handler import paper_trading_handler
        await paper_trading_handler.execute_buy_order(update, context, symbol, amount)
    except Exception as e:
        logger.error(f"❌ Error executing buy order: {e}")
        await update.callback_query.edit_message_text("❌ Could not execute buy order.")

async def _handle_paper_execute_sell(update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str, amount: float):
    """Handle executing sell order"""
    try:
        from handlers.paper_trading_handler import paper_trading_handler
        await paper_trading_handler.execute_sell_order(update, context, symbol, amount)
    except Exception as e:
        logger.error(f"❌ Error executing sell order: {e}")
        await update.callback_query.edit_message_text("❌ Could not execute sell order.")

async def _handle_paper_portfolio(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle paper portfolio view"""
    try:
        from handlers.paper_trading_handler import paper_trading_handler
        await paper_trading_handler.show_paper_trading_menu(update, context)
    except Exception as e:
        logger.error(f"❌ Error in paper portfolio: {e}")
        await update.callback_query.edit_message_text("❌ Could not load portfolio.")

async def _handle_paper_history(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle paper trading history"""
    user_id = update.effective_user.id

    try:
        from core.paper_trading import paper_trading_engine

        trades = await paper_trading_engine.get_user_trades(user_id, 10)

        if not trades:
            text = """
📈 **TRADE HISTORY** 📈

❌ **Geen trades gevonden**

Je hebt nog geen paper trades uitgevoerd.
Begin met handelen om je geschiedenis te zien!
            """
        else:
            text = "📈 **TRADE HISTORY** 📈\n\n"
            for trade in trades:
                side_icon = "💰" if trade['side'] == 'buy' else "💸"
                text += f"{side_icon} {trade['side'].upper()} {trade['amount']:.4f} {trade['symbol']} @ ${trade['price']:.4f}\n"
                text += f"   💵 ${trade['value']:.2f} - {trade['timestamp'][:10]}\n\n"

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 Terug naar Trading", callback_data="paper_trading_menu")]
        ])

        await update.callback_query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error in paper history: {e}")
        await update.callback_query.edit_message_text("❌ Could not load trade history.")

async def _handle_paper_leaderboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle paper trading leaderboard"""
    try:
        from core.paper_trading import paper_trading_engine

        leaderboard = await paper_trading_engine.get_leaderboard(10)

        text = "🏆 **PAPER TRADING LEADERBOARD** 🏆\n\n"

        if not leaderboard:
            text += "❌ Nog geen traders op het leaderboard.\nWees de eerste!"
        else:
            for i, trader in enumerate(leaderboard, 1):
                medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                text += f"{medal} {trader['username']}: ${trader['balance']:,.2f}\n"
                text += f"   📊 {trader['trade_count']} trades\n\n"

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 Terug naar Trading", callback_data="paper_trading_menu")]
        ])

        await update.callback_query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error in paper leaderboard: {e}")
        await update.callback_query.edit_message_text("❌ Could not load leaderboard.")

async def _handle_paper_reset(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle paper trading reset"""
    user_id = update.effective_user.id

    try:
        from core.paper_trading import paper_trading_engine

        # Reset portfolio
        portfolio = await paper_trading_engine.reset_portfolio(user_id)

        text = """
🔄 **PORTFOLIO RESET** 🔄

✅ **Portfolio succesvol gereset!**

💰 **Nieuwe Balance:** $10,000.00
📊 **Posities:** Alle verkocht
📈 **Statistieken:** Gereset

Je kunt nu opnieuw beginnen met paper trading!
        """

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🚀 Begin Trading", callback_data="paper_trading_menu")]
        ])

        await update.callback_query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error in paper reset: {e}")
        await update.callback_query.edit_message_text("❌ Could not reset portfolio.")

# Export handlers for main.py
__all__ = [
    'start_handler',
    'help_handler',
    'register_handler',
    'admin_handler',
    'trading_handler',
    'callback_handler',
    'message_handler',
    'set_bot_manager'
]