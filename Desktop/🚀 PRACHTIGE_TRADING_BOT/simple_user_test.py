#!/usr/bin/env python3
"""Simple UserProfile test"""

print("🧪 Testing UserProfile...")

try:
    from core.user_manager import UserProfile
    
    # Test basic creation
    user = UserProfile(user_id=123, username="test")
    print(f"✅ Basic test: {user.user_id}, {user.username}")
    
    # Test with database-like data
    user_data = {
        'user_id': 456,
        'username': 'db_test',
        'first_name': 'Test',
        'role': 'regular'
    }
    
    # Simulate the fixed _create_user_profile method
    data_copy = user_data.copy()
    user_id = data_copy.pop('user_id', None)
    username = data_copy.pop('username', None)
    
    user2 = UserProfile(user_id=user_id, username=username, **data_copy)
    print(f"✅ Database simulation: {user2.user_id}, {user2.username}, {user2.first_name}")
    
    print("🎉 All tests passed!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
