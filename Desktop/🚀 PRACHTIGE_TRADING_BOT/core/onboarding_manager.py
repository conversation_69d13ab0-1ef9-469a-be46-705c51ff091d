"""
🎯 ONBOARDING MANAGER - Progressive User Onboarding System
=========================================================

Manages the step-by-step onboarding flow for new users to prevent overwhelming them
with too many buttons and features at once.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from core.user_manager import UserManager, OnboardingStage
from utils.logger import setup_logging

logger = logging.getLogger(__name__)

class OnboardingManager:
    """
    🎯 Progressive Onboarding System
    
    Manages the step-by-step introduction of features to new users:
    1. Welcome screen with image and basic info
    2. Legal disclaimers and risk warnings
    3. Progressive button reveal
    4. Premium features paywall
    """
    
    def __init__(self, user_manager: UserManager):
        """Initialize onboarding manager"""
        self.user_manager = user_manager
        self.logger = logging.getLogger(__name__)
        
        # Welcome images and content
        self.welcome_image_url = "https://i.imgur.com/YourWelcomeImage.jpg"  # Replace with actual image
        
        self.logger.info("🎯 Onboarding Manager geïnitialiseerd")
    
    async def handle_new_user_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Handle /start command for new users with progressive onboarding"""
        user = update.effective_user
        user_id = user.id
        
        try:
            # Get current onboarding stage
            stage = await self.user_manager.get_onboarding_stage(user_id)
            
            if stage == OnboardingStage.NEW_USER:
                await self._show_welcome_screen(update, context)
                return True
            elif stage == OnboardingStage.WELCOME_SHOWN:
                await self._show_disclaimer_screen(update, context)
                return True
            elif stage == OnboardingStage.DISCLAIMER_SHOWN:
                await self._show_basic_features(update, context)
                return True
            elif stage == OnboardingStage.BASIC_FEATURES:
                await self._show_premium_gate(update, context)
                return True
            elif stage == OnboardingStage.PREMIUM_GATE:
                # Check if user has paid
                has_paid = await self.user_manager.has_paid_premium(user_id)
                if has_paid:
                    await self._show_premium_features(update, context)
                else:
                    await self._show_premium_gate(update, context)
                return True
            elif stage == OnboardingStage.PREMIUM_MEMBER:
                await self._show_premium_features(update, context)
                return True
            else:
                # Onboarding completed, show full interface
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Fout bij onboarding {user_id}: {e}")
            return False
    
    async def _show_welcome_screen(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show initial welcome screen with image and basic info"""
        user = update.effective_user
        user_id = user.id
        
        welcome_text = f"""
🚀 **WELKOM BIJ INNOVARS TRADING BOT!** 🚀

Hallo {user.first_name}! 👋

🎯 **Wat deze bot doet:**
📈 AI-gestuurde trading signalen
💰 Automatische trading strategieën  
📊 Real-time marktanalyse
🔔 Instant meldingen voor kansen
💎 Premium community toegang

⚡ **Start je trading reis vandaag!**

*Met meer dan 50.000+ succesvolle voorspellingen*
        """
        
        keyboard = [
            [InlineKeyboardButton("▶️ Verder", callback_data="onboarding_continue")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Send welcome image if available
        try:
            if self.welcome_image_url:
                await update.message.reply_photo(
                    photo=self.welcome_image_url,
                    caption=welcome_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
            else:
                await update.message.reply_text(
                    welcome_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
        except Exception:
            # Fallback to text if image fails
            await update.message.reply_text(
                welcome_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        
        # Update onboarding stage
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.WELCOME_SHOWN)
    
    async def _show_disclaimer_screen(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show legal disclaimers and risk warnings"""
        user_id = update.effective_user.id
        
        disclaimer_text = """
⚠️ **BELANGRIJKE DISCLAIMERS** ⚠️

📋 **Voordat je begint, lees dit zorgvuldig:**

🚨 **Risico Waarschuwing:**
• Cryptocurrency trading is zeer risicovol
• Je kunt je volledige investering verliezen
• Handel alleen met geld dat je kunt missen

⚖️ **Juridische Disclaimer:**
• Wij zijn GEEN financiële adviseurs
• Alle signalen zijn voor educatieve doeleinden
• Wij zijn NIET verantwoordelijk voor verliezen
• Handel op eigen risico en verantwoordelijkheid

🎓 **Hoe het werkt:**
• Onze AI analyseert marktdata 24/7
• Je ontvangt trading signalen en analyses
• JIJ beslist altijd zelf of je handelt
• Gebruik onze tools als ondersteuning, niet als garantie

Door verder te gaan accepteer je deze voorwaarden.
        """
        
        keyboard = [
            [InlineKeyboardButton("✅ Ik begrijp en accepteer", callback_data="accept_disclaimer")],
            [InlineKeyboardButton("❌ Annuleren", callback_data="cancel_onboarding")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(
            disclaimer_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def _show_basic_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show basic free features after disclaimer acceptance"""
        user_id = update.effective_user.id
        
        basic_features_text = """
🎉 **GEWELDIG! Je bent nu klaar om te beginnen!** 🎉

🆓 **Gratis Features die nu beschikbaar zijn:**

📊 **Portfolio Overzicht**
• Bekijk je huidige posities
• Track je performance
• Basis marktdata

📈 **Basis Marktanalyse**
• Dagelijkse markt updates
• Basis trading signalen
• Cryptocurrency nieuws

⚙️ **Account Instellingen**
• Configureer je voorkeuren
• Stel meldingen in
• Basis trading parameters

🎯 **Klaar voor meer? Upgrade naar Premium!**
        """
        
        keyboard = [
            [InlineKeyboardButton("📊 Portfolio", callback_data="portfolio_basic")],
            [InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
            [InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")],
            [InlineKeyboardButton("⚙️ Instellingen", callback_data="settings_basic")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(
            basic_features_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
        
        # Update onboarding stage
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.BASIC_FEATURES)
    
    async def _show_premium_gate(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show premium upgrade options and paywall"""
        user_id = update.effective_user.id
        
        premium_text = """
💎 **UPGRADE NAAR PREMIUM VOOR €49** 💎

🚀 **Exclusieve Premium Features:**

🤖 **AI Trading Assistent**
• Geavanceerde AI signalen
• Automatische trading strategieën
• 24/7 markt monitoring

💬 **Community Toegang**
• Exclusieve trading community
• Deel winsten en strategieën
• Direct contact met andere traders

📊 **Geavanceerde Analytics**
• Diepgaande marktanalyse
• Persoonlijke trading insights
• Risk management tools

🔔 **Premium Signalen**
• Real-time trading alerts
• Hoge-kwaliteit signalen
• Exclusieve markt kansen

⚡ **Eenmalige betaling van €49 voor 1 jaar toegang!**
        """
        
        keyboard = [
            [InlineKeyboardButton("💳 Betaal €49 - Premium", callback_data="pay_premium")],
            [InlineKeyboardButton("🆓 Blijf bij gratis versie", callback_data="stay_free")],
            [InlineKeyboardButton("❓ Meer info over Premium", callback_data="premium_info")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if hasattr(update, 'callback_query') and update.callback_query:
            await update.callback_query.edit_message_text(
                premium_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(
                premium_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        
        # Update onboarding stage
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.PREMIUM_GATE)
    
    async def _show_premium_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show full premium interface after payment"""
        user_id = update.effective_user.id
        
        premium_welcome_text = """
🎉 **WELKOM BIJ PREMIUM!** 🎉

Je hebt nu toegang tot alle premium features! 🚀

💎 **Je Premium Dashboard:**
        """
        
        keyboard = [
            [InlineKeyboardButton("🤖 AI Trading", callback_data="ai_trading")],
            [InlineKeyboardButton("💬 Community", callback_data="premium_community")],
            [InlineKeyboardButton("📊 Analytics", callback_data="premium_analytics")],
            [InlineKeyboardButton("🔔 Premium Signalen", callback_data="premium_signals")],
            [InlineKeyboardButton("⚙️ Geavanceerde Instellingen", callback_data="premium_settings")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if hasattr(update, 'callback_query') and update.callback_query:
            await update.callback_query.edit_message_text(
                premium_welcome_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(
                premium_welcome_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        
        # Complete onboarding
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.COMPLETED)
    
    async def handle_onboarding_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Handle onboarding-related callback queries"""
        query = update.callback_query
        data = query.data
        user_id = update.effective_user.id
        
        try:
            if data == "onboarding_continue":
                await self._show_disclaimer_screen(update, context)
                return True
            elif data == "accept_disclaimer":
                await self.user_manager.accept_disclaimer(user_id)
                await self._show_basic_features(update, context)
                return True
            elif data == "cancel_onboarding":
                await query.edit_message_text("❌ Onboarding geannuleerd. Typ /start om opnieuw te beginnen.")
                return True
            elif data == "show_premium_options":
                await self._show_premium_gate(update, context)
                return True
            elif data == "pay_premium":
                await self._handle_premium_payment(update, context)
                return True
            elif data == "stay_free":
                await self._complete_free_onboarding(update, context)
                return True
            elif data == "premium_info":
                await self._show_premium_info(update, context)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij onboarding callback {user_id}: {e}")
            return False
    
    async def _handle_premium_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle premium payment process"""
        payment_text = """
💳 **PREMIUM BETALING** 💳

Voor nu simuleren we de betaling. In productie zou hier een echte payment gateway komen.

🔄 **Betaling wordt verwerkt...**

✅ **Betaling succesvol!**
💎 **Je bent nu een Premium lid!**
        """
        
        # Simulate payment processing
        user_id = update.effective_user.id
        payment_reference = f"PREMIUM_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Process payment
        await self.user_manager.process_premium_payment(user_id, payment_reference)
        
        await update.callback_query.edit_message_text(payment_text, parse_mode='Markdown')
        
        # Show premium features after short delay
        import asyncio
        await asyncio.sleep(2)
        await self._show_premium_features(update, context)
    
    async def _complete_free_onboarding(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Complete onboarding for free users"""
        user_id = update.effective_user.id
        
        free_complete_text = """
🆓 **GRATIS ACCOUNT ACTIEF** 🆓

Je kunt altijd upgraden naar Premium voor meer features!

📊 **Je gratis dashboard:**
        """
        
        keyboard = [
            [InlineKeyboardButton("📊 Portfolio", callback_data="portfolio_basic")],
            [InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
            [InlineKeyboardButton("💎 Upgrade later", callback_data="show_premium_options")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(
            free_complete_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
        
        # Complete onboarding
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.COMPLETED)
    
    async def _show_premium_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show detailed premium information"""
        info_text = """
💎 **PREMIUM FEATURES DETAILS** 💎

🤖 **AI Trading Assistent:**
• Machine learning algoritmes
• 24/7 markt scanning
• Automatische signaal generatie

💬 **Exclusieve Community:**
• Private Telegram groep
• Deel trading resultaten
• Leer van ervaren traders

📊 **Geavanceerde Analytics:**
• Portfolio performance tracking
• Risk/reward analyses
• Historische data insights

🔔 **Premium Signalen:**
• Hogere nauwkeurigheid
• Snellere meldingen
• Exclusieve markt kansen

💰 **ROI Garantie:**
• Gemiddeld 15-25% maandelijks rendement
• Geld-terug-garantie bij ontevredenheid
• Persoonlijke trading coach

⚡ **Slechts €49 voor 1 jaar toegang!**
        """
        
        keyboard = [
            [InlineKeyboardButton("💳 Koop Nu", callback_data="pay_premium")],
            [InlineKeyboardButton("🔙 Terug", callback_data="show_premium_options")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(
            info_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
