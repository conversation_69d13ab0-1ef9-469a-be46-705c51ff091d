"""
🎯 ONBOARDING MANAGER - Progressive User Onboarding System
=========================================================

Manages the step-by-step onboarding flow for new users to prevent overwhelming them
with too many buttons and features at once.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from core.user_manager import UserManager, OnboardingStage
from utils.logger import setup_logging

logger = logging.getLogger(__name__)

class OnboardingManager:
    """
    🎯 Progressive Onboarding System

    Manages the step-by-step introduction of features to new users:
    1. Welcome screen with image and basic info
    2. Legal disclaimers and risk warnings
    3. Progressive button reveal
    4. Premium features paywall
    """

    def __init__(self, user_manager: UserManager):
        """Initialize onboarding manager"""
        self.user_manager = user_manager
        self.logger = logging.getLogger(__name__)

        # Welcome images and content
        self.welcome_image_url = "https://i.imgur.com/YourWelcomeImage.jpg"  # Replace with actual image

        self.logger.info("🎯 Onboarding Manager geïnitialiseerd")

    async def handle_new_user_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Handle /start command for new users with progressive onboarding"""
        user = update.effective_user
        user_id = user.id

        try:
            # Get current onboarding stage
            stage = await self.user_manager.get_onboarding_stage(user_id)

            if stage == OnboardingStage.NEW_USER:
                await self._show_welcome_screen(update, context)
                return True
            elif stage == OnboardingStage.WELCOME_SHOWN:
                await self._show_disclaimer_screen(update, context)
                return True
            elif stage == OnboardingStage.DISCLAIMER_SHOWN:
                await self._show_basic_features(update, context)
                return True
            elif stage == OnboardingStage.BASIC_FEATURES:
                await self._show_premium_gate(update, context)
                return True
            elif stage == OnboardingStage.PREMIUM_GATE:
                # Check if user has paid
                has_paid = await self.user_manager.has_paid_premium(user_id)
                if has_paid:
                    await self._show_premium_features(update, context)
                else:
                    await self._show_premium_gate(update, context)
                return True
            elif stage == OnboardingStage.PREMIUM_MEMBER:
                await self._show_premium_features(update, context)
                return True
            else:
                # Onboarding completed, show full interface
                return False

        except Exception as e:
            self.logger.error(f"❌ Fout bij onboarding {user_id}: {e}")
            return False

    async def _show_welcome_screen(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show enhanced welcome screen with complete bot introduction and registration"""
        user = update.effective_user
        user_id = user.id

        welcome_text = f"""
🚀 **WELKOM BIJ PRACHTIGE TRADING BOT!** 🚀

👋 Hallo {user.first_name}!

🤖 **Wat is deze bot?**
Ik ben je persoonlijke AI trading assistent die je helpt met cryptocurrency trading. Deze bot is ontwikkeld door ervaren traders en AI-specialisten om je te helpen succesvol te handelen.

💰 **Wat kan ik voor je doen?**
✅ **Automatisch Trading** - Laat de AI voor je handelen
✅ **Marktanalyse & Signalen** - Realtime marktinzichten
✅ **Portfolio Management** - Beheer je investeringen
✅ **Strategieën & Backtesting** - Test strategieën voordat je ze gebruikt
✅ **Community Toegang** - Leer van andere traders

⚠️ **BELANGRIJKE DISCLAIMER:**
🔴 **Wij zijn GEEN financiële adviseurs**
🔴 **Trading brengt ALTIJD risico's met zich mee**
🔴 **Investeer ALLEEN wat je kunt missen**
🔴 **Verleden prestaties garanderen geen toekomstige resultaten**

📊 **Wat krijg je GRATIS:**
✅ Basis marktdata en prijzen
✅ Eenvoudige trading signalen
✅ Portfolio tracking
✅ Educatieve content

💎 **Premium Features (€49 eenmalig):**
✅ Geavanceerde AI trading strategieën
✅ Realtime premium signalen
✅ Toegang tot trading community
✅ Persoonlijke begeleiding
✅ Geavanceerde analytics

👥 **Vrienden uitnodigen:**
Nodig vrienden uit en krijg bonussen!

🎯 **Klaar om je trading journey te beginnen?**
        """

        keyboard = [
            [InlineKeyboardButton("🚀 Ja, laten we beginnen!", callback_data="onboarding_continue")],
            [InlineKeyboardButton("👥 Vrienden uitnodigen", callback_data="invite_friends")],
            [InlineKeyboardButton("❌ Misschien later", callback_data="cancel_onboarding")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # Send welcome message
        if update.message:
            await update.message.reply_text(welcome_text, reply_markup=reply_markup, parse_mode='Markdown')
        else:
            await update.callback_query.edit_message_text(welcome_text, reply_markup=reply_markup, parse_mode='Markdown')

        # Update onboarding stage
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.WELCOME_SHOWN)

    async def _show_name_registration(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show name registration step"""
        user = update.effective_user
        user_id = user.id

        registration_text = f"""
📝 **REGISTRATIE STAP 1/2** 📝

Hallo {user.first_name}!

Om je de beste ervaring te geven, hebben we wat basis informatie nodig:

👤 **Je naam:** {user.first_name} {user.last_name or ''}
📧 **Telegram:** @{user.username or 'Geen username'}
🆔 **User ID:** {user_id}

✅ **Deze informatie wordt gebruikt voor:**
• Persoonlijke trading aanbevelingen
• Community interacties
• Support en begeleiding
• Referral bonussen

🔒 **Privacy:** Je gegevens worden veilig opgeslagen en nooit gedeeld met derden.

Klaar om door te gaan?
        """

        keyboard = [
            [InlineKeyboardButton("✅ Ja, registreer me", callback_data="name_registered")],
            [InlineKeyboardButton("🔙 Terug", callback_data="back_to_welcome")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            registration_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )

    async def _show_referral_system(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show referral system information"""
        user = update.effective_user
        user_id = user.id

        # Generate referral link
        referral_link = f"https://t.me/mijntradingsbot?start=ref_{user_id}"

        referral_text = f"""
👥 **VRIENDEN UITNODIGEN & BONUSSEN VERDIENEN!** 👥

🎁 **Verdien met elke uitnodiging:**
• €5 bonus voor elke vriend die zich registreert
• €10 extra als je vriend Premium wordt
• Onbeperkt aantal uitnodigingen mogelijk!

🔗 **Jouw persoonlijke uitnodigingslink:**
`{referral_link}`

📱 **Hoe het werkt:**
1. Deel je link met vrienden
2. Zij klikken en registreren zich
3. Jij krijgt automatisch je bonus!
4. Bonussen worden toegevoegd aan je account

💰 **Bonus voorwaarden:**
• Vriend moet actief blijven voor 7 dagen
• Premium bonus na succesvolle betaling
• Bonussen zijn uitbetaalbaar of te gebruiken voor trading

📊 **Je huidige referrals:**
• Geregistreerd: 0 vrienden
• Premium upgrades: 0 vrienden
• Totaal verdiend: €0

Deel je link en begin met verdienen! 🚀
        """

        keyboard = [
            [InlineKeyboardButton("📋 Kopieer Link", callback_data=f"copy_referral_{user_id}")],
            [InlineKeyboardButton("📱 Deel via Telegram", url=f"https://t.me/share/url?url={referral_link}")],
            [InlineKeyboardButton("🔙 Terug naar registratie", callback_data="back_to_welcome")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            referral_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )

    async def _show_disclaimer_screen(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show legal disclaimers and risk warnings"""
        user_id = update.effective_user.id

        disclaimer_text = """
⚠️ **BELANGRIJKE DISCLAIMERS** ⚠️

📋 **Voordat je begint, lees dit zorgvuldig:**

🚨 **Risico Waarschuwing:**
• Cryptocurrency trading is zeer risicovol
• Je kunt je volledige investering verliezen
• Handel alleen met geld dat je kunt missen

⚖️ **Juridische Disclaimer:**
• Wij zijn GEEN financiële adviseurs
• Alle signalen zijn voor educatieve doeleinden
• Wij zijn NIET verantwoordelijk voor verliezen
• Handel op eigen risico en verantwoordelijkheid

🎓 **Hoe het werkt:**
• Onze AI analyseert marktdata 24/7
• Je ontvangt trading signalen en analyses
• JIJ beslist altijd zelf of je handelt
• Gebruik onze tools als ondersteuning, niet als garantie

Door verder te gaan accepteer je deze voorwaarden.
        """

        keyboard = [
            [InlineKeyboardButton("✅ Ik begrijp en accepteer", callback_data="accept_disclaimer")],
            [InlineKeyboardButton("❌ Annuleren", callback_data="cancel_onboarding")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            disclaimer_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )

    async def _show_basic_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show enhanced basic free features with step-by-step introduction"""
        user_id = update.effective_user.id

        basic_features_text = """
🎉 **GEWELDIG! WELKOM BIJ JE GRATIS TRADING ACCOUNT!** 🎉

✅ **Je account is nu actief en klaar voor gebruik!**

🆓 **GRATIS FEATURES - Stap voor stap introductie:**

**STAP 1: LEER DE BASIS** 📚
📊 **Portfolio Dashboard** - Bekijk je trading overzicht
📈 **Marktdata & Prijzen** - Realtime cryptocurrency prijzen
📰 **Crypto Nieuws** - Blijf op de hoogte van markt ontwikkelingen

**STAP 2: BEGIN MET TRADING** 🚀
🎯 **Basis Trading Signalen** - Eenvoudige koop/verkoop signalen
📋 **Trading Educatie** - Leer hoe je succesvol handelt
⚙️ **Account Instellingen** - Configureer je voorkeuren

**STAP 3: TRACK JE PRESTATIES** 📊
📈 **Performance Tracking** - Volg je winsten en verliezen
📊 **Basis Analytics** - Simpele trading statistieken
🎯 **Trading Doelen** - Stel realistische doelen

**WAT KUN JE VERWACHTEN?**
✅ Dagelijkse markt updates
✅ 3-5 basis trading signalen per week
✅ Educatieve content en tips
✅ Basis portfolio tracking
✅ Community toegang (beperkt)

💎 **Klaar voor meer? Upgrade naar Premium voor geavanceerde features!**

🎯 **Waar wil je beginnen?**
        """

        keyboard = [
            [InlineKeyboardButton("📚 1. Leer de Basis", callback_data="learn_basics")],
            [InlineKeyboardButton("🚀 2. Begin Trading", callback_data="start_trading_basic")],
            [InlineKeyboardButton("📊 3. Portfolio Dashboard", callback_data="portfolio_basic")],
            [InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")],
            [InlineKeyboardButton("⚙️ Instellingen", callback_data="settings_basic")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            basic_features_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )

        # Update onboarding stage
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.BASIC_FEATURES)

    async def _show_premium_gate(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show enhanced premium upgrade options and paywall"""
        user_id = update.effective_user.id

        premium_text = """
💎 **UPGRADE NAAR PREMIUM - EENMALIG €49** 💎

🚀 **EXCLUSIEVE PREMIUM FEATURES:**

**🤖 GEAVANCEERDE AI TRADING**
✅ AI-gestuurde trading strategieën met 85%+ nauwkeurigheid
✅ Automatische trading bots die 24/7 voor je werken
✅ Realtime markt scanning en analyse
✅ Persoonlijke AI trading assistent

**💬 EXCLUSIEVE COMMUNITY TOEGANG**
✅ Toegang tot premium trading community bot (@Innovars_chatbot)
✅ Deel je winsten en strategieën met andere traders
✅ Leer van succesvolle premium traders
✅ Exclusieve groepschats en discussies
✅ Photo sharing van trading resultaten

**📊 GEAVANCEERDE ANALYTICS & TOOLS**
✅ Diepgaande portfolio analyse en performance tracking
✅ Risk management tools en stop-loss strategieën
✅ Backtesting van strategieën met historische data
✅ Persoonlijke trading insights en aanbevelingen

**🔔 PREMIUM SIGNALEN & ALERTS**
✅ 15-25 premium signalen per week (vs 3-5 gratis)
✅ Realtime push notificaties voor trading kansen
✅ Exclusieve "whale alert" meldingen
✅ Early access tot nieuwe coin listings

**🎯 PERSOONLIJKE BEGELEIDING**
✅ 1-op-1 trading coaching sessies
✅ Persoonlijke trading plan ontwikkeling
✅ Direct contact met trading experts
✅ Prioriteit support en hulp

**💰 BEWEZEN RESULTATEN:**
• Gemiddeld 15-25% maandelijks rendement
• 85%+ nauwkeurigheid van premium signalen
• Meer dan 10.000+ tevreden premium leden

⚡ **EENMALIGE BETALING €49 = 1 JAAR VOLLEDIGE TOEGANG!**
💸 **Geld-terug-garantie binnen 30 dagen**

🎁 **BONUS: Gratis toegang tot community bot ter waarde van €25!**
        """

        keyboard = [
            [InlineKeyboardButton("💳 Betaal €49 - Word Premium!", callback_data="pay_premium")],
            [InlineKeyboardButton("❓ Meer details & voorbeelden", callback_data="premium_info")],
            [InlineKeyboardButton("🆓 Blijf bij gratis versie", callback_data="stay_free")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        if hasattr(update, 'callback_query') and update.callback_query:
            await update.callback_query.edit_message_text(
                premium_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(
                premium_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        # Update onboarding stage
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.PREMIUM_GATE)

    async def _show_premium_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show full premium interface with step-by-step guide after payment"""
        user_id = update.effective_user.id

        premium_welcome_text = """
🎉 **GEFELICITEERD! WELKOM BIJ PREMIUM!** 🎉

✅ **Je betaling is succesvol verwerkt!**
💎 **Je hebt nu toegang tot ALLE premium features!**

🚀 **PREMIUM STAP-VOOR-STAP GIDS:**

**STAP 1: COMMUNITY TOEGANG** 💬
• Ga naar @Innovars_chatbot (community bot)
• Deel je trading resultaten met foto's
• Leer van andere premium traders
• Krijg exclusieve tips en strategieën

**STAP 2: AI TRADING ACTIVEREN** 🤖
• Configureer je AI trading assistent
• Stel je trading parameters in
• Activeer automatische trading
• Monitor je AI trading prestaties

**STAP 3: PREMIUM SIGNALEN** 🔔
• Ontvang 15-25 premium signalen per week
• Realtime push notificaties
• Exclusieve whale alerts
• Early access nieuwe coins

**STAP 4: GEAVANCEERDE ANALYTICS** 📊
• Bekijk je uitgebreide portfolio analyse
• Gebruik risk management tools
• Backtest je trading strategieën
• Krijg persoonlijke trading insights

**STAP 5: PERSOONLIJKE BEGELEIDING** 🎯
• Boek je eerste 1-op-1 coaching sessie
• Ontwikkel je persoonlijke trading plan
• Krijg direct contact met experts
• Prioriteit support en hulp

🎁 **BONUS FEATURES ONTGRENDELD:**
✅ Community bot toegang (@Innovars_chatbot)
✅ Geavanceerde trading tools
✅ Exclusieve markt analyses
✅ Premium support kanaal

💰 **VERWACHTE RESULTATEN:**
• 15-25% gemiddeld maandelijks rendement
• 85%+ nauwkeurigheid van signalen
• Toegang tot winnende strategieën

🚀 **Waar wil je beginnen?**
        """

        keyboard = [
            [InlineKeyboardButton("💬 1. Community Bot Bezoeken", url="https://t.me/Innovars_chatbot")],
            [InlineKeyboardButton("🤖 2. AI Trading Setup", callback_data="ai_trading_setup")],
            [InlineKeyboardButton("🔔 3. Premium Signalen", callback_data="premium_signals")],
            [InlineKeyboardButton("📊 4. Analytics Dashboard", callback_data="premium_analytics")],
            [InlineKeyboardButton("🎯 5. Persoonlijke Coaching", callback_data="premium_coaching")],
            [InlineKeyboardButton("⚙️ Geavanceerde Instellingen", callback_data="premium_settings")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        if hasattr(update, 'callback_query') and update.callback_query:
            await update.callback_query.edit_message_text(
                premium_welcome_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(
                premium_welcome_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        # Complete onboarding
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.COMPLETED)

    async def handle_onboarding_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Handle onboarding-related callback queries"""
        query = update.callback_query
        data = query.data
        user_id = update.effective_user.id

        try:
            if data == "onboarding_continue":
                await self._show_name_registration(update, context)
                return True
            elif data == "invite_friends":
                await self._show_referral_system(update, context)
                return True
            elif data == "name_registered":
                await self._show_disclaimer_screen(update, context)
                return True
            elif data == "accept_disclaimer":
                await self.user_manager.accept_disclaimer(user_id)
                await self._show_basic_features(update, context)
                return True
            elif data == "cancel_onboarding":
                await query.edit_message_text("❌ Onboarding geannuleerd. Typ /start om opnieuw te beginnen.")
                return True
            elif data == "show_premium_options":
                await self._show_premium_gate(update, context)
                return True
            elif data == "pay_premium":
                await self._handle_premium_payment(update, context)
                return True
            elif data == "stay_free":
                await self._complete_free_onboarding(update, context)
                return True
            elif data == "premium_info":
                await self._show_premium_info(update, context)
                return True
            elif data == "back_to_welcome":
                await self._show_welcome_screen(update, context)
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ Fout bij onboarding callback {user_id}: {e}")
            return False

    async def _handle_premium_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle premium payment process"""
        payment_text = """
💳 **PREMIUM BETALING** 💳

Voor nu simuleren we de betaling. In productie zou hier een echte payment gateway komen.

🔄 **Betaling wordt verwerkt...**

✅ **Betaling succesvol!**
💎 **Je bent nu een Premium lid!**
        """

        # Simulate payment processing
        user_id = update.effective_user.id
        payment_reference = f"PREMIUM_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Process payment
        await self.user_manager.process_premium_payment(user_id, payment_reference)

        await update.callback_query.edit_message_text(payment_text, parse_mode='Markdown')

        # Show premium features after short delay
        import asyncio
        await asyncio.sleep(2)
        await self._show_premium_features(update, context)

    async def _complete_free_onboarding(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Complete onboarding for free users"""
        user_id = update.effective_user.id

        free_complete_text = """
🆓 **GRATIS ACCOUNT ACTIEF** 🆓

Je kunt altijd upgraden naar Premium voor meer features!

📊 **Je gratis dashboard:**
        """

        keyboard = [
            [InlineKeyboardButton("📊 Portfolio", callback_data="portfolio_basic")],
            [InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")],
            [InlineKeyboardButton("💎 Upgrade later", callback_data="show_premium_options")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            free_complete_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )

        # Complete onboarding
        await self.user_manager.update_onboarding_stage(user_id, OnboardingStage.COMPLETED)

    async def _show_premium_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show detailed premium information"""
        info_text = """
💎 **PREMIUM FEATURES DETAILS** 💎

🤖 **AI Trading Assistent:**
• Machine learning algoritmes
• 24/7 markt scanning
• Automatische signaal generatie

💬 **Exclusieve Community:**
• Private Telegram groep
• Deel trading resultaten
• Leer van ervaren traders

📊 **Geavanceerde Analytics:**
• Portfolio performance tracking
• Risk/reward analyses
• Historische data insights

🔔 **Premium Signalen:**
• Hogere nauwkeurigheid
• Snellere meldingen
• Exclusieve markt kansen

💰 **ROI Garantie:**
• Gemiddeld 15-25% maandelijks rendement
• Geld-terug-garantie bij ontevredenheid
• Persoonlijke trading coach

⚡ **Slechts €49 voor 1 jaar toegang!**
        """

        keyboard = [
            [InlineKeyboardButton("💳 Koop Nu", callback_data="pay_premium")],
            [InlineKeyboardButton("🔙 Terug", callback_data="show_premium_options")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            info_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
