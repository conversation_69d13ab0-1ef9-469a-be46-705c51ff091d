"""
⌨️ PROGRESSIVE KEYBOARDS - State-based Button System
===================================================

Creates keyboards that progressively reveal buttons based on user's onboarding stage
and premium status to prevent overwhelming new users.
"""

import logging
from typing import List, Optional
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, KeyboardButton, ReplyKeyboardMarkup

from core.user_manager import UserManager, OnboardingStage, UserRole

logger = logging.getLogger(__name__)

class ProgressiveKeyboards:
    """
    ⌨️ Progressive Keyboard System

    Creates different keyboard layouts based on:
    - User's onboarding stage
    - Premium status
    - Admin privileges
    """

    def __init__(self, user_manager: UserManager):
        """Initialize progressive keyboards"""
        self.user_manager = user_manager
        self.logger = logging.getLogger(__name__)

    async def get_main_keyboard(self, user_id: int) -> Optional[InlineKeyboardMarkup]:
        """Get main keyboard based on user's current stage and status"""
        try:
            # Get user info
            user = await self.user_manager.get_user(user_id)
            if not user:
                return self._get_new_user_keyboard()

            stage = user.onboarding_stage
            is_premium = await self.user_manager.has_paid_premium(user_id)
            is_admin = await self.user_manager.is_admin(user_id)

            # Admin gets full access
            if is_admin:
                return self._get_admin_keyboard()

            # Progressive keyboards based on onboarding stage
            if stage == OnboardingStage.NEW_USER:
                return None  # No buttons for brand new users
            elif stage == OnboardingStage.WELCOME_SHOWN:
                return None  # Still in onboarding flow
            elif stage == OnboardingStage.DISCLAIMER_SHOWN:
                return None  # Still in onboarding flow
            elif stage == OnboardingStage.BASIC_FEATURES:
                return self._get_basic_features_keyboard()
            elif stage == OnboardingStage.PREMIUM_GATE:
                return self._get_premium_gate_keyboard()
            elif stage == OnboardingStage.PREMIUM_MEMBER or is_premium:
                return self._get_premium_keyboard()
            elif stage == OnboardingStage.COMPLETED:
                if is_premium:
                    return self._get_premium_keyboard()
                else:
                    return self._get_free_user_keyboard()
            else:
                # Default to basic features
                return self._get_basic_features_keyboard()

        except Exception as e:
            self.logger.error(f"❌ Fout bij genereren keyboard voor {user_id}: {e}")
            return self._get_basic_features_keyboard()

    def _get_new_user_keyboard(self) -> None:
        """No keyboard for brand new users - they see onboarding flow"""
        return None

    def _get_basic_features_keyboard(self) -> InlineKeyboardMarkup:
        """Basic features keyboard for free users who completed disclaimer"""
        keyboard = [
            [
                InlineKeyboardButton("📊 Portfolio", callback_data="portfolio_basic"),
                InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")
            ],
            [
                InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu"),
                InlineKeyboardButton("📰 Nieuws", callback_data="news_basic")
            ],
            [
                InlineKeyboardButton("⚙️ Instellingen", callback_data="settings_basic"),
                InlineKeyboardButton("❓ Help", callback_data="help_basic")
            ],
            [
                InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def _get_premium_gate_keyboard(self) -> InlineKeyboardMarkup:
        """Keyboard shown when user reaches premium gate"""
        keyboard = [
            [
                InlineKeyboardButton("💳 Premium €49", callback_data="pay_premium"),
                InlineKeyboardButton("🆓 Gratis blijven", callback_data="stay_free")
            ],
            [
                InlineKeyboardButton("❓ Premium Info", callback_data="premium_info")
            ],
            [
                InlineKeyboardButton("📊 Basis Features", callback_data="portfolio_basic")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def _get_free_user_keyboard(self) -> InlineKeyboardMarkup:
        """Full keyboard for free users who completed onboarding"""
        keyboard = [
            [
                InlineKeyboardButton("📊 Portfolio", callback_data="portfolio_basic"),
                InlineKeyboardButton("📈 Marktdata", callback_data="market_basic")
            ],
            [
                InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu"),
                InlineKeyboardButton("🔔 Basis Signalen", callback_data="signals_basic")
            ],
            [
                InlineKeyboardButton("📰 Crypto Nieuws", callback_data="news_basic"),
                InlineKeyboardButton("⚙️ Instellingen", callback_data="settings_basic")
            ],
            [
                InlineKeyboardButton("💎 Upgrade naar Premium", callback_data="show_premium_options")
            ],
            [
                InlineKeyboardButton("❓ Help", callback_data="help_basic")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def _get_premium_keyboard(self) -> InlineKeyboardMarkup:
        """Full premium keyboard with all features"""
        keyboard = [
            [
                InlineKeyboardButton("🤖 AI Trading", callback_data="ai_trading"),
                InlineKeyboardButton("📊 Premium Analytics", callback_data="premium_analytics")
            ],
            [
                InlineKeyboardButton("💬 Community", callback_data="premium_community"),
                InlineKeyboardButton("🔔 Premium Signalen", callback_data="premium_signals")
            ],
            [
                InlineKeyboardButton("📈 Auto Trading", callback_data="auto_trading"),
                InlineKeyboardButton("📊 Paper Trading", callback_data="paper_trading_menu")
            ],
            [
                InlineKeyboardButton("💰 Portfolio Pro", callback_data="portfolio_premium"),
                InlineKeyboardButton("📊 Markt Scanner", callback_data="market_scanner")
            ],
            [
                InlineKeyboardButton("🎯 Risk Manager", callback_data="risk_manager"),
                InlineKeyboardButton("🎓 Trading Academy", callback_data="trading_academy")
            ],
            [
                InlineKeyboardButton("⚙️ Premium Settings", callback_data="settings_premium")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    def _get_admin_keyboard(self) -> InlineKeyboardMarkup:
        """Admin keyboard with management features"""
        keyboard = [
            [
                InlineKeyboardButton("👑 Admin Panel", callback_data="admin_panel"),
                InlineKeyboardButton("📊 User Stats", callback_data="admin_stats")
            ],
            [
                InlineKeyboardButton("💰 Payments", callback_data="admin_payments"),
                InlineKeyboardButton("📢 Broadcast", callback_data="admin_broadcast")
            ],
            [
                InlineKeyboardButton("🤖 AI Trading", callback_data="ai_trading"),
                InlineKeyboardButton("💬 Community", callback_data="premium_community")
            ],
            [
                InlineKeyboardButton("⚙️ System Settings", callback_data="admin_settings"),
                InlineKeyboardButton("📋 Logs", callback_data="admin_logs")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)

    async def get_persistent_keyboard(self, user_id: int) -> Optional[ReplyKeyboardMarkup]:
        """Get persistent keyboard (bottom buttons) based on user status"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return None

            is_premium = await self.user_manager.has_paid_premium(user_id)
            is_admin = await self.user_manager.is_admin(user_id)
            stage = user.onboarding_stage

            # No persistent keyboard during onboarding
            if stage in [OnboardingStage.NEW_USER, OnboardingStage.WELCOME_SHOWN, OnboardingStage.DISCLAIMER_SHOWN]:
                return None

            # Admin persistent keyboard
            if is_admin:
                keyboard = [
                    [KeyboardButton("👑 Admin"), KeyboardButton("💹 Trading")],
                    [KeyboardButton("📊 Analytics"), KeyboardButton("💬 Community")]
                ]
                return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            # Premium persistent keyboard
            if is_premium:
                keyboard = [
                    [KeyboardButton("🤖 AI Trading"), KeyboardButton("📊 Portfolio")],
                    [KeyboardButton("💬 Community"), KeyboardButton("🔔 Signalen")]
                ]
                return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            # Basic persistent keyboard for free users
            keyboard = [
                [KeyboardButton("📊 Portfolio"), KeyboardButton("📈 Markt")],
                [KeyboardButton("💎 Premium"), KeyboardButton("❓ Help")]
            ]
            return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

        except Exception as e:
            self.logger.error(f"❌ Fout bij genereren persistent keyboard voor {user_id}: {e}")
            return None

    async def get_trading_keyboard(self, user_id: int) -> Optional[InlineKeyboardMarkup]:
        """Get trading keyboard based on user's premium status"""
        try:
            is_premium = await self.user_manager.has_paid_premium(user_id)
            is_admin = await self.user_manager.is_admin(user_id)

            if is_admin or is_premium:
                # Full trading keyboard for premium users
                keyboard = [
                    [
                        InlineKeyboardButton("🛒 Koop", callback_data="quick_buy"),
                        InlineKeyboardButton("💰 Verkoop", callback_data="quick_sell"),
                        InlineKeyboardButton("⏹️ Stop All", callback_data="stop_all")
                    ],
                    [
                        InlineKeyboardButton("📊 Markt Order", callback_data="market_order"),
                        InlineKeyboardButton("⏱️ Limit Order", callback_data="limit_order")
                    ],
                    [
                        InlineKeyboardButton("🤖 Auto Trading", callback_data="auto_trading_toggle"),
                        InlineKeyboardButton("📈 Strategieën", callback_data="trading_strategies")
                    ],
                    [
                        InlineKeyboardButton("📊 Posities", callback_data="open_positions"),
                        InlineKeyboardButton("📜 Geschiedenis", callback_data="trade_history")
                    ],
                    [
                        InlineKeyboardButton("🔙 Hoofdmenu", callback_data="main_menu")
                    ]
                ]
            else:
                # Limited trading keyboard for free users
                keyboard = [
                    [
                        InlineKeyboardButton("📊 Demo Trading", callback_data="demo_trading"),
                        InlineKeyboardButton("📈 Markt Kijken", callback_data="market_view")
                    ],
                    [
                        InlineKeyboardButton("💎 Upgrade voor Trading", callback_data="show_premium_options")
                    ],
                    [
                        InlineKeyboardButton("🔙 Hoofdmenu", callback_data="main_menu")
                    ]
                ]

            return InlineKeyboardMarkup(keyboard)

        except Exception as e:
            self.logger.error(f"❌ Fout bij genereren trading keyboard voor {user_id}: {e}")
            return None

    async def get_portfolio_keyboard(self, user_id: int) -> Optional[InlineKeyboardMarkup]:
        """Get portfolio keyboard based on user's premium status"""
        try:
            is_premium = await self.user_manager.has_paid_premium(user_id)
            is_admin = await self.user_manager.is_admin(user_id)

            if is_admin or is_premium:
                # Full portfolio keyboard for premium users
                keyboard = [
                    [
                        InlineKeyboardButton("💰 Balans", callback_data="portfolio_balance"),
                        InlineKeyboardButton("📊 Performance", callback_data="portfolio_performance")
                    ],
                    [
                        InlineKeyboardButton("📈 P&L", callback_data="portfolio_pnl"),
                        InlineKeyboardButton("🎯 Allocatie", callback_data="portfolio_allocation")
                    ],
                    [
                        InlineKeyboardButton("📋 Alle Posities", callback_data="all_positions"),
                        InlineKeyboardButton("⚡ Snelle Acties", callback_data="quick_actions")
                    ],
                    [
                        InlineKeyboardButton("📊 Analytics", callback_data="portfolio_analytics"),
                        InlineKeyboardButton("📈 Trends", callback_data="portfolio_trends")
                    ],
                    [
                        InlineKeyboardButton("🔙 Hoofdmenu", callback_data="main_menu")
                    ]
                ]
            else:
                # Basic portfolio keyboard for free users
                keyboard = [
                    [
                        InlineKeyboardButton("💰 Basis Balans", callback_data="portfolio_basic_balance"),
                        InlineKeyboardButton("📊 Overzicht", callback_data="portfolio_basic_overview")
                    ],
                    [
                        InlineKeyboardButton("💎 Upgrade voor meer", callback_data="show_premium_options")
                    ],
                    [
                        InlineKeyboardButton("🔙 Hoofdmenu", callback_data="main_menu")
                    ]
                ]

            return InlineKeyboardMarkup(keyboard)

        except Exception as e:
            self.logger.error(f"❌ Fout bij genereren portfolio keyboard voor {user_id}: {e}")
            return None
