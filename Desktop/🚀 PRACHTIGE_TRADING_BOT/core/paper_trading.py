#!/usr/bin/env python3
"""
📊 PAPER TRADING SYSTEM - Virtual Trading Environment
====================================================

Complete paper trading system for testing strategies without real money.
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class PaperTradingEngine:
    """
    📊 Paper Trading Engine
    
    Simulates real trading with virtual money for testing strategies.
    """
    
    def __init__(self, initial_balance: float = 10000.0):
        """Initialize paper trading engine"""
        self.initial_balance = initial_balance
        self.data_file = Path("database/paper_trades.json")
        self.data_file.parent.mkdir(exist_ok=True)
        
        # Load existing data
        self.trades = self._load_trades()
        self.portfolios = self._load_portfolios()
        
        logger.info(f"📊 Paper Trading Engine initialized with ${initial_balance:,.2f}")
    
    def _load_trades(self) -> List[Dict]:
        """Load paper trades from file"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                    return data.get('trades', [])
            return []
        except Exception as e:
            logger.error(f"❌ Error loading trades: {e}")
            return []
    
    def _load_portfolios(self) -> Dict:
        """Load user portfolios from file"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                    return data.get('portfolios', {})
            return {}
        except Exception as e:
            logger.error(f"❌ Error loading portfolios: {e}")
            return {}
    
    def _save_data(self):
        """Save all data to file"""
        try:
            data = {
                'trades': self.trades,
                'portfolios': self.portfolios,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"❌ Error saving data: {e}")
    
    async def create_portfolio(self, user_id: int, username: str) -> Dict:
        """Create new paper trading portfolio for user"""
        user_key = str(user_id)
        
        if user_key not in self.portfolios:
            self.portfolios[user_key] = {
                'user_id': user_id,
                'username': username,
                'balance': self.initial_balance,
                'positions': {},
                'total_invested': 0.0,
                'total_profit_loss': 0.0,
                'trade_count': 0,
                'winning_trades': 0,
                'created_at': datetime.now().isoformat(),
                'last_activity': datetime.now().isoformat()
            }
            self._save_data()
            logger.info(f"📊 Created paper portfolio for {username} (${self.initial_balance:,.2f})")
        
        return self.portfolios[user_key]
    
    async def get_portfolio(self, user_id: int) -> Optional[Dict]:
        """Get user's paper trading portfolio"""
        user_key = str(user_id)
        return self.portfolios.get(user_key)
    
    async def place_order(self, user_id: int, symbol: str, side: str, amount: float, price: float) -> Dict:
        """Place a paper trading order"""
        user_key = str(user_id)
        portfolio = self.portfolios.get(user_key)
        
        if not portfolio:
            raise ValueError("Portfolio not found. Create portfolio first.")
        
        # Calculate order value
        order_value = amount * price
        
        # Check if user has enough balance for buy orders
        if side.lower() == 'buy':
            if portfolio['balance'] < order_value:
                raise ValueError(f"Insufficient balance. Available: ${portfolio['balance']:.2f}, Required: ${order_value:.2f}")
        
        # Check if user has enough position for sell orders
        elif side.lower() == 'sell':
            current_position = portfolio['positions'].get(symbol, 0)
            if current_position < amount:
                raise ValueError(f"Insufficient {symbol} position. Available: {current_position}, Required: {amount}")
        
        # Create trade record
        trade = {
            'id': len(self.trades) + 1,
            'user_id': user_id,
            'symbol': symbol,
            'side': side.lower(),
            'amount': amount,
            'price': price,
            'value': order_value,
            'timestamp': datetime.now().isoformat(),
            'status': 'filled'
        }
        
        # Update portfolio
        if side.lower() == 'buy':
            # Deduct balance and add position
            portfolio['balance'] -= order_value
            portfolio['positions'][symbol] = portfolio['positions'].get(symbol, 0) + amount
            portfolio['total_invested'] += order_value
        
        elif side.lower() == 'sell':
            # Add balance and reduce position
            portfolio['balance'] += order_value
            portfolio['positions'][symbol] -= amount
            if portfolio['positions'][symbol] <= 0:
                del portfolio['positions'][symbol]
        
        # Update stats
        portfolio['trade_count'] += 1
        portfolio['last_activity'] = datetime.now().isoformat()
        
        # Save trade and portfolio
        self.trades.append(trade)
        self._save_data()
        
        logger.info(f"📊 Paper trade executed: {side.upper()} {amount} {symbol} @ ${price:.4f}")
        return trade
    
    async def get_portfolio_value(self, user_id: int, current_prices: Dict[str, float]) -> Dict:
        """Calculate current portfolio value"""
        portfolio = await self.get_portfolio(user_id)
        if not portfolio:
            return {}
        
        total_value = portfolio['balance']
        position_values = {}
        
        for symbol, amount in portfolio['positions'].items():
            current_price = current_prices.get(symbol, 0)
            position_value = amount * current_price
            position_values[symbol] = {
                'amount': amount,
                'current_price': current_price,
                'value': position_value
            }
            total_value += position_value
        
        profit_loss = total_value - self.initial_balance
        profit_loss_percent = (profit_loss / self.initial_balance) * 100
        
        return {
            'total_value': total_value,
            'cash_balance': portfolio['balance'],
            'positions': position_values,
            'profit_loss': profit_loss,
            'profit_loss_percent': profit_loss_percent,
            'initial_balance': self.initial_balance
        }
    
    async def get_user_trades(self, user_id: int, limit: int = 10) -> List[Dict]:
        """Get user's recent trades"""
        user_trades = [trade for trade in self.trades if trade['user_id'] == user_id]
        return sorted(user_trades, key=lambda x: x['timestamp'], reverse=True)[:limit]
    
    async def get_leaderboard(self, limit: int = 10) -> List[Dict]:
        """Get paper trading leaderboard"""
        # This would need current prices to calculate accurate values
        # For now, return basic stats
        leaderboard = []
        
        for user_key, portfolio in self.portfolios.items():
            leaderboard.append({
                'user_id': portfolio['user_id'],
                'username': portfolio['username'],
                'balance': portfolio['balance'],
                'total_invested': portfolio['total_invested'],
                'trade_count': portfolio['trade_count'],
                'winning_trades': portfolio['winning_trades']
            })
        
        # Sort by balance (simplified)
        leaderboard.sort(key=lambda x: x['balance'], reverse=True)
        return leaderboard[:limit]
    
    async def reset_portfolio(self, user_id: int) -> Dict:
        """Reset user's portfolio to initial state"""
        user_key = str(user_id)
        if user_key in self.portfolios:
            username = self.portfolios[user_key]['username']
            self.portfolios[user_key] = {
                'user_id': user_id,
                'username': username,
                'balance': self.initial_balance,
                'positions': {},
                'total_invested': 0.0,
                'total_profit_loss': 0.0,
                'trade_count': 0,
                'winning_trades': 0,
                'created_at': datetime.now().isoformat(),
                'last_activity': datetime.now().isoformat()
            }
            self._save_data()
            logger.info(f"📊 Reset paper portfolio for {username}")
            return self.portfolios[user_key]
        return {}

class MockPriceProvider:
    """Mock price provider for paper trading"""
    
    def __init__(self):
        """Initialize with some mock prices"""
        self.prices = {
            'BTC': 43250.00,
            'ETH': 2580.00,
            'ADA': 0.52,
            'SOL': 98.50,
            'DOGE': 0.085,
            'MATIC': 0.89,
            'LINK': 14.25,
            'DOT': 7.80,
            'AVAX': 36.50,
            'UNI': 6.75
        }
    
    async def get_price(self, symbol: str) -> float:
        """Get current price for symbol"""
        # Add some random variation to simulate price movement
        import random
        base_price = self.prices.get(symbol.upper(), 1.0)
        variation = random.uniform(-0.02, 0.02)  # ±2% variation
        return base_price * (1 + variation)
    
    async def get_prices(self, symbols: List[str]) -> Dict[str, float]:
        """Get prices for multiple symbols"""
        prices = {}
        for symbol in symbols:
            prices[symbol] = await self.get_price(symbol)
        return prices

# Global instances
paper_trading_engine = PaperTradingEngine()
price_provider = MockPriceProvider()
