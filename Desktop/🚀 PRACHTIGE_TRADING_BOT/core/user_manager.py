"""
👤 USER MANAGER - User Management System
=======================================

Manages user accounts, permissions, and user-related operations.
"""

import logging
import secrets
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any

from database.database_manager import DatabaseManager
from utils.encryption import encrypt_data, decrypt_data

logger = logging.getLogger(__name__)

class UserRole(Enum):
    """User role types"""
    REGULAR = "regular"
    PREMIUM = "premium"
    VIP = "vip"
    ADMIN = "admin"

class UserStatus(Enum):
    """User status types"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    BANNED = "banned"
    INACTIVE = "inactive"

class OnboardingStage(Enum):
    """Onboarding stages for progressive user experience"""
    NEW_USER = "new_user"                    # Just joined, no buttons shown
    WELCOME_SHOWN = "welcome_shown"          # Welcome image/message shown
    DISCLAIMER_SHOWN = "disclaimer_shown"    # Legal disclaimers shown
    BASIC_FEATURES = "basic_features"        # Basic free features unlocked
    PREMIUM_GATE = "premium_gate"            # Shown premium upgrade options
    PREMIUM_MEMBER = "premium_member"        # Paid €49, full access
    COMPLETED = "completed"                  # Onboarding fully completed

class UserProfile:
    """User profile data"""
    def __init__(self, user_id: int, username: str = None, **kwargs):
        self.user_id = user_id
        self.username = username
        self.first_name = kwargs.get('first_name', '')
        self.last_name = kwargs.get('last_name', '')
        self.role = UserRole(kwargs.get('role', UserRole.REGULAR.value))
        self.status = UserStatus(kwargs.get('status', UserStatus.ACTIVE.value))
        self.created_at = kwargs.get('created_at', datetime.now())
        self.last_activity = kwargs.get('last_activity', datetime.now())

        # Trading settings
        self.trading_enabled = kwargs.get('trading_enabled', False)
        self.auto_trading = kwargs.get('auto_trading', False)
        self.api_keys_configured = kwargs.get('api_keys_configured', False)
        self.default_trading_amount = kwargs.get('default_trading_amount', 100.0)
        self.default_risk_level = kwargs.get('default_risk_level', 'medium')
        self.enabled_strategies = kwargs.get('enabled_strategies', [])

        # Premium status
        self.premium_until = kwargs.get('premium_until', None)
        self.vip_until = kwargs.get('vip_until', None)

        # Onboarding tracking
        self.onboarding_stage = OnboardingStage(kwargs.get('onboarding_stage', OnboardingStage.NEW_USER.value))
        self.onboarding_completed_at = kwargs.get('onboarding_completed_at', None)
        self.disclaimer_accepted = kwargs.get('disclaimer_accepted', False)
        self.disclaimer_accepted_at = kwargs.get('disclaimer_accepted_at', None)

        # Payment tracking
        self.payment_status = kwargs.get('payment_status', 'unpaid')  # unpaid, pending, paid, failed
        self.payment_amount = kwargs.get('payment_amount', 0.0)
        self.payment_date = kwargs.get('payment_date', None)
        self.payment_reference = kwargs.get('payment_reference', None)

        # Statistics
        self.total_trades = kwargs.get('total_trades', 0)
        self.winning_trades = kwargs.get('winning_trades', 0)
        self.total_volume = kwargs.get('total_volume', 0.0)
        self.total_pnl = kwargs.get('total_pnl', 0.0)

        # Preferences
        self.language = kwargs.get('language', 'en')
        self.timezone = kwargs.get('timezone', 'UTC')

        # Suspension/ban info
        self.suspension_reason = kwargs.get('suspension_reason', None)
        self.suspended_at = kwargs.get('suspended_at', None)
        self.ban_reason = kwargs.get('ban_reason', None)
        self.banned_at = kwargs.get('banned_at', None)

class UserSession:
    """User session data"""
    def __init__(self, user_id: int, token: str, created_at: datetime, expires_at: datetime, last_activity: datetime):
        self.user_id = user_id
        self.token = token
        self.created_at = created_at
        self.expires_at = expires_at
        self.last_activity = last_activity

class UserManager:
    """
    👤 User Management System

    Manages all user-related operations:
    - User registration and authentication
    - User profiles and preferences
    - Permission management
    - API key storage and retrieval
    - User statistics
    """

    def __init__(self, database_manager: DatabaseManager):
        """Initialize user manager"""
        self.db = database_manager
        self.logger = logging.getLogger(__name__)

        # Cache for active sessions
        self.active_sessions: Dict[int, UserSession] = {}

        # Admin user IDs (from config)
        self.admin_user_ids: List[int] = []

        self.logger.info("👤 User Manager geïnitialiseerd")

    def set_admin_user_ids(self, admin_ids: List[int]):
        """Set admin user IDs from config"""
        self.admin_user_ids = admin_ids
        self.logger.info(f"👑 Admin user IDs set: {admin_ids}")

    async def register_user(self, user_id: int, username: str = None, first_name: str = None, last_name: str = None) -> UserProfile:
        """Register a new user or update existing user"""
        try:
            # Check if user already exists
            existing_user = await self.get_user(user_id)

            if existing_user:
                # Update user data if needed
                updates = {}
                if username and username != existing_user.username:
                    updates['username'] = username
                if first_name and first_name != existing_user.first_name:
                    updates['first_name'] = first_name
                if last_name and last_name != existing_user.last_name:
                    updates['last_name'] = last_name

                if updates:
                    await self.update_user(user_id, updates)
                    self.logger.info(f"👤 User {user_id} geüpdatet")

                return await self.get_user(user_id)

            # Create new user
            user_data = {
                'user_id': user_id,
                'username': username,
                'first_name': first_name,
                'last_name': last_name,
                'role': UserRole.REGULAR.value,
                'status': UserStatus.ACTIVE.value,
                'created_at': datetime.now(),
                'last_activity': datetime.now(),
                'trading_enabled': False,
                'auto_trading': False,
                'api_keys_configured': False,
                'default_trading_amount': 100.0,
                'default_risk_level': 'medium',
                'enabled_strategies': [],
                'language': 'en',
                'timezone': 'UTC',
                'total_trades': 0,
                'winning_trades': 0,
                'total_volume': 0.0,
                'total_pnl': 0.0,

                # Onboarding fields
                'onboarding_stage': OnboardingStage.NEW_USER.value,
                'onboarding_completed_at': None,
                'disclaimer_accepted': False,
                'disclaimer_accepted_at': None,

                # Payment fields
                'payment_status': 'unpaid',
                'payment_amount': 0.0,
                'payment_date': None,
                'payment_reference': None
            }

            # Check if user is admin
            if user_id in self.admin_user_ids:
                user_data['role'] = UserRole.ADMIN.value

            # Create user in database
            await self.db.create_user(user_data)

            self.logger.info(f"👤 Nieuwe user geregistreerd: {user_id}")
            return self._create_user_profile(user_data)

        except Exception as e:
            self.logger.error(f"❌ Fout bij registreren user {user_id}: {e}")
            # Return minimal user profile
            return UserProfile(user_id, username, first_name=first_name, last_name=last_name)

    async def get_user(self, user_id: int) -> Optional[UserProfile]:
        """Get user by ID"""
        try:
            user_data = await self.db.get_user(user_id)
            if not user_data:
                return None

            return self._create_user_profile(user_data)

        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen user {user_id}: {e}")
            return None

    async def get_or_create_user(self, user_id: int, username: str = None, first_name: str = None, last_name: str = None) -> UserProfile:
        """Get existing user or create new one"""
        user = await self.get_user(user_id)
        if user:
            return user

        return await self.register_user(user_id, username, first_name, last_name)

    async def update_user(self, user_id: int, updates: Dict[str, Any]) -> bool:
        """Update user data"""
        try:
            await self.db.update_user(user_id, updates)
            self.logger.debug(f"👤 User {user_id} geüpdatet: {updates.keys()}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Fout bij updaten user {user_id}: {e}")
            return False

    async def update_last_activity(self, user_id: int) -> bool:
        """Update user's last activity timestamp"""
        try:
            await self.update_user(user_id, {'last_activity': datetime.now()})
            return True

        except Exception as e:
            self.logger.error(f"❌ Fout bij updaten last activity {user_id}: {e}")
            return False

    async def is_admin(self, user_id: int) -> bool:
        """Check if user is admin"""
        # First check admin list from config
        if user_id in self.admin_user_ids:
            return True

        # Then check database
        try:
            user = await self.get_user(user_id)
            return user and user.role == UserRole.ADMIN

        except Exception as e:
            self.logger.error(f"❌ Fout bij admin check {user_id}: {e}")
            return False

    async def is_premium(self, user_id: int) -> bool:
        """Check if user has premium status"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False

            # Admin is always premium
            if user.role == UserRole.ADMIN:
                return True

            # VIP is also premium
            if user.role == UserRole.VIP:
                return True

            # Check premium status
            if user.role == UserRole.PREMIUM:
                # Check if premium has expired
                if user.premium_until and user.premium_until > datetime.now():
                    return True
                else:
                    # Premium expired, downgrade to regular
                    await self.update_user(user_id, {'role': UserRole.REGULAR.value})
                    return False

            return False

        except Exception as e:
            self.logger.error(f"❌ Fout bij premium check {user_id}: {e}")
            return False

    async def is_vip(self, user_id: int) -> bool:
        """Check if user has VIP status"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False

            # Admin is always VIP
            if user.role == UserRole.ADMIN:
                return True

            # Check VIP status
            if user.role == UserRole.VIP:
                # Check if VIP has expired
                if user.vip_until and user.vip_until > datetime.now():
                    return True
                else:
                    # VIP expired, downgrade to premium or regular
                    if user.premium_until and user.premium_until > datetime.now():
                        await self.update_user(user_id, {'role': UserRole.PREMIUM.value})
                    else:
                        await self.update_user(user_id, {'role': UserRole.REGULAR.value})
                    return False

            return False

        except Exception as e:
            self.logger.error(f"❌ Fout bij VIP check {user_id}: {e}")
            return False

    # =================================================================================
    # 🎯 ONBOARDING MANAGEMENT
    # =================================================================================

    async def get_onboarding_stage(self, user_id: int) -> OnboardingStage:
        """Get user's current onboarding stage"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return OnboardingStage.NEW_USER

            return user.onboarding_stage
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen onboarding stage {user_id}: {e}")
            return OnboardingStage.NEW_USER

    async def update_onboarding_stage(self, user_id: int, stage: OnboardingStage) -> bool:
        """Update user's onboarding stage"""
        try:
            updates = {'onboarding_stage': stage.value}

            # If completing onboarding, set completion timestamp
            if stage == OnboardingStage.COMPLETED:
                updates['onboarding_completed_at'] = datetime.now()

            await self.update_user(user_id, updates)
            self.logger.info(f"🎯 User {user_id} onboarding stage updated to: {stage.value}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Fout bij updaten onboarding stage {user_id}: {e}")
            return False

    async def accept_disclaimer(self, user_id: int) -> bool:
        """Mark that user has accepted the legal disclaimer"""
        try:
            updates = {
                'disclaimer_accepted': True,
                'disclaimer_accepted_at': datetime.now()
            }
            await self.update_user(user_id, updates)
            self.logger.info(f"📋 User {user_id} accepted disclaimer")
            return True
        except Exception as e:
            self.logger.error(f"❌ Fout bij accepteren disclaimer {user_id}: {e}")
            return False

    async def has_accepted_disclaimer(self, user_id: int) -> bool:
        """Check if user has accepted the disclaimer"""
        try:
            user = await self.get_user(user_id)
            return user and user.disclaimer_accepted
        except Exception as e:
            self.logger.error(f"❌ Fout bij check disclaimer {user_id}: {e}")
            return False

    # =================================================================================
    # 💰 PAYMENT MANAGEMENT
    # =================================================================================

    async def process_premium_payment(self, user_id: int, payment_reference: str, amount: float = 49.0) -> bool:
        """Process premium membership payment"""
        try:
            # Update payment status
            updates = {
                'payment_status': 'paid',
                'payment_amount': amount,
                'payment_date': datetime.now(),
                'payment_reference': payment_reference,
                'role': UserRole.PREMIUM.value,
                'premium_until': datetime.now() + timedelta(days=365),  # 1 year premium
                'onboarding_stage': OnboardingStage.PREMIUM_MEMBER.value
            }

            await self.update_user(user_id, updates)
            self.logger.info(f"💰 Premium payment processed for user {user_id}: €{amount}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Fout bij verwerken payment {user_id}: {e}")
            return False

    async def has_paid_premium(self, user_id: int) -> bool:
        """Check if user has paid for premium access"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False

            # Admin always has premium
            if user.role == UserRole.ADMIN:
                return True

            return user.payment_status == 'paid' and user.payment_amount >= 49.0
        except Exception as e:
            self.logger.error(f"❌ Fout bij check premium payment {user_id}: {e}")
            return False

    async def get_payment_status(self, user_id: int) -> str:
        """Get user's payment status"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return 'unpaid'
            return user.payment_status
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen payment status {user_id}: {e}")
            return 'unpaid'

    def _create_user_profile(self, user_data: Dict[str, Any]) -> UserProfile:
        """Create UserProfile object from database data"""
        # Extract user_id and username separately to avoid duplicate keyword arguments
        user_id = user_data.get('user_id')
        username = user_data.get('username')

        # Create a copy without user_id and username to avoid conflicts
        kwargs = {k: v for k, v in user_data.items() if k not in ['user_id', 'username']}

        return UserProfile(
            user_id=user_id,
            username=username,
            **kwargs
        )

    async def store_api_keys(self, user_id: int, exchange: str, api_data: Dict[str, str]) -> bool:
        """Store encrypted API keys for user"""
        try:
            # Encrypt data
            encrypted_data = {}
            for key, value in api_data.items():
                if value:
                    encrypted_data[key] = encrypt_data(value)

            # Store in database
            await self.db.store_user_api_keys(user_id, exchange, encrypted_data)

            # Update user settings
            await self.update_user(user_id, {
                'api_keys_configured': True,
                'trading_enabled': True
            })

            self.logger.info(f"🔑 API keys opgeslagen voor user {user_id} ({exchange})")
            return True

        except Exception as e:
            self.logger.error(f"❌ Fout bij opslaan API keys {user_id}: {e}")
            return False

    async def get_api_keys(self, user_id: int, exchange: str) -> Optional[Dict[str, str]]:
        """Get decrypted API keys for user"""
        try:
            encrypted_data = await self.db.get_user_api_keys(user_id, exchange)
            if not encrypted_data:
                return None

            # Decrypt data
            decrypted_data = {}
            for key, encrypted_value in encrypted_data.items():
                if encrypted_value:
                    decrypted_data[key] = decrypt_data(encrypted_value)

            return decrypted_data

        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen API keys {user_id}: {e}")
            return None

    async def delete_api_keys(self, user_id: int, exchange: str) -> bool:
        """Delete API keys for user"""
        try:
            await self.db.delete_user_api_keys(user_id, exchange)

            # Check if user has any other API keys configured
            remaining_keys = await self.db.get_user_all_api_keys(user_id)

            if not remaining_keys:
                await self.update_user(user_id, {
                    'api_keys_configured': False,
                    'trading_enabled': False
                })

            self.logger.info(f"🗑️ API keys verwijderd voor user {user_id} ({exchange})")
            return True

        except Exception as e:
            self.logger.error(f"❌ Fout bij verwijderen API keys {user_id}: {e}")
            return False

    async def update_trading_preferences(self, user_id: int, preferences: Dict[str, Any]) -> bool:
        """Update user's trading preferences"""
        try:
            allowed_fields = [
                'default_trading_amount',
                'default_risk_level',
                'auto_trading',
                'enabled_strategies',
                'language',
                'timezone'
            ]

            # Filter only allowed fields
            updates = {k: v for k, v in preferences.items() if k in allowed_fields}

            if updates:
                await self.update_user(user_id, updates)
                self.logger.info(f"⚙️ Trading preferences geüpdatet voor user {user_id}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ Fout bij updaten preferences {user_id}: {e}")
            return False

    async def update_trading_stats(self, user_id: int, trade_result: Dict[str, Any]) -> bool:
        """Update user's trading statistics"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False

            # Calculate new stats
            new_total_trades = user.total_trades + 1
            new_total_volume = user.total_volume + trade_result.get('volume', 0)
            new_total_pnl = user.total_pnl + trade_result.get('pnl', 0)

            new_winning_trades = user.winning_trades
            if trade_result.get('pnl', 0) > 0:
                new_winning_trades += 1

            updates = {
                'total_trades': new_total_trades,
                'winning_trades': new_winning_trades,
                'total_volume': new_total_volume,
                'total_pnl': new_total_pnl,
            }

            await self.update_user(user_id, updates)
            return True

        except Exception as e:
            self.logger.error(f"❌ Fout bij updaten trading stats {user_id}: {e}")
            return False

    async def get_user_stats(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive user statistics"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return {}

            # Calculate derived stats
            win_rate = 0.0
            if user.total_trades > 0:
                win_rate = user.winning_trades / user.total_trades

            avg_trade_size = 0.0
            if user.total_trades > 0:
                avg_trade_size = user.total_volume / user.total_trades

            # Get recent activity
            recent_trades = await self.db.get_user_recent_trades(user_id, limit=10)

            return {
                'user_id': user.user_id,
                'role': user.role.value,
                'member_since': user.created_at,
                'last_activity': user.last_activity,
                'trading_enabled': user.trading_enabled,
                'auto_trading': user.auto_trading,

                # Trading stats
                'total_trades': user.total_trades,
                'winning_trades': user.winning_trades,
                'losing_trades': user.total_trades - user.winning_trades,
                'win_rate': win_rate,
                'total_volume': user.total_volume,
                'total_pnl': user.total_pnl,
                'avg_trade_size': avg_trade_size,

                # Current status
                'balance': await self._get_user_balance(user_id),
                'open_positions': await self._get_user_open_positions(user_id),
                'daily_pnl': await self._get_user_daily_pnl(user_id),

                # Premium status
                'is_premium': await self.is_premium(user_id),
                'is_vip': await self.is_vip(user_id),
                'premium_until': user.premium_until,
                'vip_until': user.vip_until,

                # Recent activity
                'recent_trades': recent_trades,
                'enabled_strategies': user.enabled_strategies,
            }

        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen user stats {user_id}: {e}")
            return {}

    async def _get_user_balance(self, user_id: int) -> float:
        """Get user's current balance"""
        # This would integrate with the trading engine
        # For now, return a placeholder
        return 1000.0

    async def _get_user_open_positions(self, user_id: int) -> int:
        """Get number of user's open positions"""
        # This would integrate with the trading engine
        return 0

    async def _get_user_daily_pnl(self, user_id: int) -> float:
        """Get user's daily PnL"""
        # This would integrate with the trading engine
        return 0.0

    async def suspend_user(self, user_id: int, reason: str = "") -> bool:
        """Suspend a user"""
        try:
            await self.update_user(user_id, {
                'status': UserStatus.SUSPENDED.value,
                'trading_enabled': False,
                'suspension_reason': reason,
                'suspended_at': datetime.now()
            })

            self.logger.warning(f"⚠️ User {user_id} suspended: {reason}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Fout bij suspenderen user {user_id}: {e}")
            return False

    async def ban_user(self, user_id: int, reason: str = "") -> bool:
        """Ban a user"""
        try:
            await self.update_user(user_id, {
                'status': UserStatus.BANNED.value,
                'trading_enabled': False,
                'ban_reason': reason,
                'banned_at': datetime.now()
            })

            # Clear API keys
            await self.db.delete_all_user_api_keys(user_id)

            self.logger.warning(f"🚫 User {user_id} banned: {reason}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Fout bij bannen user {user_id}: {e}")
            return False

    async def unban_user(self, user_id: int) -> bool:
        """Unban a user"""
        try:
            await self.update_user(user_id, {
                'status': UserStatus.ACTIVE.value,
                'ban_reason': None,
                'banned_at': None
            })

            self.logger.info(f"✅ User {user_id} unbanned")
            return True

        except Exception as e:
            self.logger.error(f"❌ Fout bij unbannen user {user_id}: {e}")
            return False

    # Admin statistics methods
    async def get_total_users(self) -> int:
        """Get total number of users"""
        return await self.db.count_users()

    async def get_active_users(self, hours: int = 24) -> int:
        """Get number of active users in last X hours"""
        since = datetime.now() - timedelta(hours=hours)
        return await self.db.count_active_users(since)

    async def get_premium_users(self) -> int:
        """Get number of premium users"""
        return await self.db.count_premium_users()

    async def get_vip_users(self) -> int:
        """Get number of VIP users"""
        return await self.db.count_vip_users()

    async def get_all_users(self) -> List[UserProfile]:
        """Get all users (admin only)"""
        try:
            users_data = await self.db.get_all_users()
            return [self._create_user_profile(user_data) for user_data in users_data]
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen alle users: {e}")
            return []

    async def get_premium_users_list(self) -> List[UserProfile]:
        """Get list of premium users"""
        try:
            users_data = await self.db.get_premium_users_list()
            return [self._create_user_profile(user_data) for user_data in users_data]
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen premium users: {e}")
            return []

    async def get_vip_users_list(self) -> List[UserProfile]:
        """Get list of VIP users"""
        try:
            users_data = await self.db.get_vip_users_list()
            return [self._create_user_profile(user_data) for user_data in users_data]
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen VIP users: {e}")
            return []

    async def search_users(self, query: str) -> List[UserProfile]:
        """Search users by username or name"""
        try:
            users_data = await self.db.search_users(query)
            return [self._create_user_profile(user_data) for user_data in users_data]
        except Exception as e:
            self.logger.error(f"❌ Fout bij zoeken users: {e}")
            return []

    async def get_user_activity_report(self, days: int = 30) -> Dict[str, Any]:
        """Get user activity report"""
        try:
            since = datetime.now() - timedelta(days=days)

            return {
                'total_users': await self.get_total_users(),
                'new_users': await self.db.count_new_users(since),
                'active_users': await self.get_active_users(days * 24),
                'premium_users': await self.get_premium_users(),
                'vip_users': await self.get_vip_users(),
                'suspended_users': await self.db.count_suspended_users(),
                'banned_users': await self.db.count_banned_users(),
                'users_with_api_keys': await self.db.count_users_with_api_keys(),
                'auto_trading_enabled': await self.db.count_auto_trading_users(),
            }

        except Exception as e:
            self.logger.error(f"❌ Fout bij activity report: {e}")
            return {}

    async def cleanup_inactive_users(self, days: int = 365) -> int:
        """Clean up users inactive for X days"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            # Get inactive users
            inactive_users = await self.db.get_inactive_users(cutoff_date)

            cleaned_count = 0
            for user_data in inactive_users:
                user_id = user_data['user_id']

                # Don't delete premium/admin users
                if user_data.get('role') in ['premium', 'vip', 'admin']:
                    continue

                # Delete API keys
                await self.db.delete_all_user_api_keys(user_id)

                # Mark as cleaned (but keep record)
                await self.update_user(user_id, {
                    'status': 'inactive_cleaned',
                    'api_keys_configured': False,
                    'trading_enabled': False
                })

                cleaned_count += 1

            self.logger.info(f"🧹 Cleaned up {cleaned_count} inactive users")
            return cleaned_count

        except Exception as e:
            self.logger.error(f"❌ Fout bij cleanup inactive users: {e}")
            return 0

    async def get_database_health(self) -> str:
        """Get database health status"""
        try:
            # Test database connection
            await self.db.test_connection()
            return "healthy"
        except Exception as e:
            self.logger.error(f"❌ Database health check failed: {e}")
            return "unhealthy"

    def set_admin_user_ids(self, admin_ids: List[int]):
        """Set admin user IDs from config"""
        self.admin_user_ids = admin_ids
        self.logger.info(f"👑 Admin users configured: {len(admin_ids)}")

    async def create_session(self, user_id: int) -> str:
        """Create user session"""
        try:
            session_token = secrets.token_urlsafe(32)
            session = UserSession(
                user_id=user_id,
                token=session_token,
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=24),
                last_activity=datetime.now()
            )

            self.active_sessions[user_id] = session
            await self.db.create_session(session)

            return session_token

        except Exception as e:
            self.logger.error(f"❌ Fout bij maken session {user_id}: {e}")
            return ""

    async def validate_session(self, user_id: int, token: str) -> bool:
        """Validate user session"""
        try:
            if user_id in self.active_sessions:
                session = self.active_sessions[user_id]
                if session.token == token and session.expires_at > datetime.now():
                    # Update last activity
                    session.last_activity = datetime.now()
                    return True

            # Check database
            session_data = await self.db.get_session(user_id, token)
            if session_data and session_data['expires_at'] > datetime.now():
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ Fout bij valideren session {user_id}: {e}")
            return False

    async def destroy_session(self, user_id: int):
        """Destroy user session"""
        try:
            if user_id in self.active_sessions:
                del self.active_sessions[user_id]

            await self.db.delete_session(user_id)

        except Exception as e:
            self.logger.error(f"❌ Fout bij verwijderen session {user_id}: {e}")
