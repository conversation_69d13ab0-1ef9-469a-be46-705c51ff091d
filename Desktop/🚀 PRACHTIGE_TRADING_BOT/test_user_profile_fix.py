#!/usr/bin/env python3
"""
🐛 TEST USER PROFILE FIX
========================

Test script to verify that the UserProfile duplicate keyword argument issue is fixed.
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_user_profile_fix():
    """Test UserProfile creation with database simulation"""
    print("🐛 Testing UserProfile fix...")
    
    try:
        from core.user_manager import UserProfile, UserManager, OnboardingStage
        from database.database_manager import DatabaseManager
        print("✅ Imports successful")
        
        # Test 1: Direct UserProfile creation
        print("\n📋 Test 1: Direct UserProfile creation")
        user1 = UserProfile(user_id=123, username="test_user")
        print(f"✅ Direct creation: {user1.user_id}, {user1.username}")
        
        # Test 2: UserProfile with kwargs
        print("\n📋 Test 2: UserProfile with kwargs")
        user2 = UserProfile(
            user_id=456, 
            username="test_user2",
            first_name="Test",
            last_name="User",
            onboarding_stage="new_user"
        )
        print(f"✅ Kwargs creation: {user2.user_id}, {user2.first_name}")
        
        # Test 3: Simulate database scenario (the problematic one)
        print("\n📋 Test 3: Database simulation scenario")
        user_data = {
            'user_id': 789,
            'username': 'db_user',
            'first_name': 'Database',
            'last_name': 'User',
            'role': 'regular',
            'status': 'active',
            'onboarding_stage': 'new_user',
            'payment_status': 'unpaid'
        }
        
        # Test the _create_user_profile method
        db_manager = DatabaseManager()
        user_manager = UserManager(db_manager)
        
        user3 = user_manager._create_user_profile(user_data)
        print(f"✅ Database simulation: {user3.user_id}, {user3.username}, {user3.first_name}")
        
        # Test 4: Full user registration flow
        print("\n📋 Test 4: Full user registration")
        await db_manager.initialize()
        
        user4 = await user_manager.register_user(999, "full_test", "Full", "Test")
        print(f"✅ Full registration: {user4.user_id}, {user4.username}, {user4.first_name}")
        
        # Test 5: User retrieval
        print("\n📋 Test 5: User retrieval")
        retrieved_user = await user_manager.get_user(999)
        if retrieved_user:
            print(f"✅ User retrieval: {retrieved_user.user_id}, {retrieved_user.username}")
        else:
            print("❌ User retrieval failed")
        
        # Test 6: Onboarding stage update
        print("\n📋 Test 6: Onboarding stage update")
        success = await user_manager.update_onboarding_stage(999, OnboardingStage.WELCOME_SHOWN)
        if success:
            updated_user = await user_manager.get_user(999)
            if updated_user:
                print(f"✅ Onboarding update: {updated_user.onboarding_stage}")
            else:
                print("❌ Could not retrieve updated user")
        else:
            print("❌ Onboarding update failed")
        
        await db_manager.close()
        
        print("\n🎉 All UserProfile tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ UserProfile test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_user_profile_fix())
    if success:
        print("\n✅ UserProfile fix is working correctly!")
    else:
        print("\n❌ UserProfile fix needs more work!")
    sys.exit(0 if success else 1)
