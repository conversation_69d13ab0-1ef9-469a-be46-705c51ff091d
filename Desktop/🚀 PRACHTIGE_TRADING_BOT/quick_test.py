#!/usr/bin/env python3
"""Quick test for onboarding system"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🧪 Quick test starting...")

try:
    print("1. Testing basic imports...")
    import logging
    from datetime import datetime
    print("✅ Basic imports OK")
    
    print("2. Testing user manager...")
    from core.user_manager import UserManager, OnboardingStage
    print("✅ UserManager import OK")
    
    print("3. Testing onboarding manager...")
    from core.onboarding_manager import OnboardingManager
    print("✅ OnboardingManager import OK")
    
    print("4. Testing progressive keyboards...")
    from core.progressive_keyboards import ProgressiveKeyboards
    print("✅ ProgressiveKeyboards import OK")
    
    print("5. Testing enum values...")
    stages = list(OnboardingStage)
    print(f"✅ Found {len(stages)} onboarding stages")
    
    print("\n🎉 All imports successful! Onboarding system is ready.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
