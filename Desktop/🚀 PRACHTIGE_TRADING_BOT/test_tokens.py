#!/usr/bin/env python3
"""
🧪 TOKEN TEST VOOR BEIDE BOTS
=============================

Test of beide Telegram bot tokens correct werken.

Author: Innovars Lab
Version: 2.0.0
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_telegram_token(token, bot_name):
    """Test een Telegram bot token"""
    try:
        from telegram import Bot
        
        logger.info(f"🧪 Testing {bot_name} token...")
        
        # Create bot instance
        bot = Bot(token=token)
        
        # Get bot info
        bot_info = await bot.get_me()
        
        logger.info(f"✅ {bot_name} token is valid!")
        logger.info(f"   Bot ID: {bot_info.id}")
        logger.info(f"   Username: @{bot_info.username}")
        logger.info(f"   Name: {bot_info.first_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ {bot_name} token test failed: {e}")
        return False

async def main():
    """Test beide bot tokens"""
    print("""
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║         🧪 TELEGRAM BOT TOKEN TEST 🧪                       ║
║                                                               ║
║    Testing both Main Bot and Community Bot tokens...         ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
    """)
    
    # Get tokens from environment
    main_token = os.getenv('TELEGRAM_BOT_TOKEN')
    community_token = os.getenv('COMMUNITY_BOT_TOKEN')
    
    logger.info("🔍 Checking token configuration...")
    
    # Check if tokens exist
    if not main_token:
        logger.error("❌ TELEGRAM_BOT_TOKEN niet gevonden in .env")
        return False
        
    if not community_token:
        logger.error("❌ COMMUNITY_BOT_TOKEN niet gevonden in .env")
        return False
    
    logger.info(f"✅ Main Bot Token: {main_token[:10]}...{main_token[-10:]}")
    logger.info(f"✅ Community Bot Token: {community_token[:10]}...{community_token[-10:]}")
    
    # Test beide tokens
    logger.info("\n🧪 Starting token tests...")
    
    main_result = await test_telegram_token(main_token, "Main Trading Bot")
    community_result = await test_telegram_token(community_token, "Community Bot")
    
    # Results
    logger.info("\n📊 TEST RESULTS:")
    logger.info(f"   Main Bot: {'✅ PASS' if main_result else '❌ FAIL'}")
    logger.info(f"   Community Bot: {'✅ PASS' if community_result else '❌ FAIL'}")
    
    if main_result and community_result:
        logger.info("\n🎉 ALL TOKENS ARE VALID!")
        logger.info("🚀 You can now start both bots with: python start_both_bots.py")
        return True
    else:
        logger.error("\n❌ Some tokens are invalid. Please check your .env file.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
