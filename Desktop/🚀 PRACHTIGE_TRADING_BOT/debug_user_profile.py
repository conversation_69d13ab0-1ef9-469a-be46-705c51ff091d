#!/usr/bin/env python3
"""
🐛 DEBUG USER PROFILE CREATION
==============================

Debug script to test UserProfile creation and fix the duplicate keyword argument issue.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_user_profile_creation():
    """Test UserProfile creation with different data scenarios"""
    print("🐛 Testing UserProfile creation...")
    
    try:
        from core.user_manager import UserProfile, OnboardingStage
        print("✅ Imports successful")
        
        # Test 1: Basic creation
        print("\n📋 Test 1: Basic UserProfile creation")
        user1 = UserProfile(user_id=123, username="test_user")
        print(f"✅ Basic creation successful: {user1.user_id}, {user1.username}")
        
        # Test 2: Creation with kwargs
        print("\n📋 Test 2: UserProfile with kwargs")
        user2 = UserProfile(
            user_id=456, 
            username="test_user2",
            first_name="Test",
            last_name="User",
            onboarding_stage="new_user"
        )
        print(f"✅ Kwargs creation successful: {user2.user_id}, {user2.first_name}")
        
        # Test 3: Simulate database data (the problematic scenario)
        print("\n📋 Test 3: Simulate database data scenario")
        user_data = {
            'user_id': 789,
            'username': 'db_user',
            'first_name': 'Database',
            'last_name': 'User',
            'role': 'regular',
            'status': 'active',
            'onboarding_stage': 'new_user',
            'payment_status': 'unpaid'
        }
        
        # Extract user_id and username separately
        user_id = user_data.get('user_id')
        username = user_data.get('username')
        
        # Create kwargs without user_id and username
        kwargs = {k: v for k, v in user_data.items() if k not in ['user_id', 'username']}
        
        print(f"   user_id: {user_id}")
        print(f"   username: {username}")
        print(f"   kwargs keys: {list(kwargs.keys())}")
        
        user3 = UserProfile(user_id=user_id, username=username, **kwargs)
        print(f"✅ Database simulation successful: {user3.user_id}, {user3.username}")
        
        # Test 4: Test the actual _create_user_profile method
        print("\n📋 Test 4: Test _create_user_profile method")
        from core.user_manager import UserManager
        from database.database_manager import DatabaseManager
        
        # Create a mock database manager
        db_manager = DatabaseManager()
        user_manager = UserManager(db_manager)
        
        # Test the problematic method
        test_data = {
            'user_id': 999,
            'username': 'method_test',
            'first_name': 'Method',
            'last_name': 'Test',
            'role': 'regular',
            'onboarding_stage': 'new_user'
        }
        
        user4 = user_manager._create_user_profile(test_data)
        print(f"✅ _create_user_profile successful: {user4.user_id}, {user4.username}")
        
        print("\n🎉 All UserProfile tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ UserProfile test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_user_profile_creation()
    sys.exit(0 if success else 1)
