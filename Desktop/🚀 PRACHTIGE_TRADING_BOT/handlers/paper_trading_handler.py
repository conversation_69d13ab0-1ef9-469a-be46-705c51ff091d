#!/usr/bin/env python3
"""
📊 PAPER TRADING HANDLERS - Telegram Bot Handlers for Paper Trading
==================================================================

Handles all paper trading related commands and interactions.
"""

import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from core.paper_trading import paper_trading_engine, price_provider

logger = logging.getLogger(__name__)

class PaperTradingHandler:
    """Handles paper trading commands and interactions"""
    
    def __init__(self):
        """Initialize paper trading handler"""
        self.engine = paper_trading_engine
        self.price_provider = price_provider
        logger.info("📊 Paper Trading Handler initialized")
    
    async def show_paper_trading_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main paper trading menu"""
        user = update.effective_user
        user_id = user.id
        
        # Create portfolio if it doesn't exist
        portfolio = await self.engine.get_portfolio(user_id)
        if not portfolio:
            portfolio = await self.engine.create_portfolio(user_id, user.username or user.first_name)
        
        # Get current portfolio value
        symbols = list(portfolio['positions'].keys()) if portfolio['positions'] else ['BTC', 'ETH']
        current_prices = await self.price_provider.get_prices(symbols)
        portfolio_value = await self.engine.get_portfolio_value(user_id, current_prices)
        
        menu_text = f"""
📊 **PAPER TRADING DASHBOARD** 📊

👤 **Trader:** {user.first_name}
💰 **Portfolio Waarde:** ${portfolio_value.get('total_value', 10000):,.2f}
💵 **Cash Balance:** ${portfolio_value.get('cash_balance', 10000):,.2f}
📈 **P&L:** ${portfolio_value.get('profit_loss', 0):+,.2f} ({portfolio_value.get('profit_loss_percent', 0):+.2f}%)

🎯 **Statistieken:**
• Totaal Trades: {portfolio['trade_count']}
• Winning Trades: {portfolio['winning_trades']}
• Success Rate: {(portfolio['winning_trades']/max(portfolio['trade_count'], 1)*100):.1f}%

📋 **Huidige Posities:**
"""
        
        if portfolio_value.get('positions'):
            for symbol, pos_data in portfolio_value['positions'].items():
                menu_text += f"• {symbol}: {pos_data['amount']:.4f} @ ${pos_data['current_price']:.4f} = ${pos_data['value']:.2f}\n"
        else:
            menu_text += "• Geen open posities\n"
        
        menu_text += "\n🚀 **Klaar om te handelen?**"
        
        keyboard = [
            [InlineKeyboardButton("💰 Kopen", callback_data="paper_buy"),
             InlineKeyboardButton("💸 Verkopen", callback_data="paper_sell")],
            [InlineKeyboardButton("📊 Portfolio Details", callback_data="paper_portfolio"),
             InlineKeyboardButton("📈 Trade History", callback_data="paper_history")],
            [InlineKeyboardButton("🏆 Leaderboard", callback_data="paper_leaderboard"),
             InlineKeyboardButton("🔄 Reset Portfolio", callback_data="paper_reset")],
            [InlineKeyboardButton("🔙 Terug naar Main Menu", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if update.callback_query:
            await update.callback_query.edit_message_text(menu_text, reply_markup=reply_markup, parse_mode='Markdown')
        else:
            await update.message.reply_text(menu_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def show_buy_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show buy menu with popular cryptocurrencies"""
        buy_text = """
💰 **PAPER TRADING - KOPEN** 💰

Selecteer een cryptocurrency om te kopen:

📊 **Huidige Prijzen:**
"""
        
        # Get current prices for popular coins
        symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOGE', 'MATIC']
        prices = await self.price_provider.get_prices(symbols)
        
        for symbol in symbols:
            price = prices.get(symbol, 0)
            buy_text += f"• {symbol}: ${price:,.4f}\n"
        
        keyboard = [
            [InlineKeyboardButton("₿ Bitcoin (BTC)", callback_data="paper_buy_BTC"),
             InlineKeyboardButton("⟠ Ethereum (ETH)", callback_data="paper_buy_ETH")],
            [InlineKeyboardButton("🔷 Cardano (ADA)", callback_data="paper_buy_ADA"),
             InlineKeyboardButton("☀️ Solana (SOL)", callback_data="paper_buy_SOL")],
            [InlineKeyboardButton("🐕 Dogecoin (DOGE)", callback_data="paper_buy_DOGE"),
             InlineKeyboardButton("🔺 Polygon (MATIC)", callback_data="paper_buy_MATIC")],
            [InlineKeyboardButton("🔙 Terug", callback_data="paper_trading_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(buy_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def show_sell_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show sell menu with user's positions"""
        user_id = update.effective_user.id
        portfolio = await self.engine.get_portfolio(user_id)
        
        if not portfolio or not portfolio['positions']:
            sell_text = """
💸 **PAPER TRADING - VERKOPEN** 💸

❌ **Geen posities om te verkopen**

Je hebt momenteel geen open posities. Koop eerst wat cryptocurrency om te kunnen verkopen.
            """
            keyboard = [
                [InlineKeyboardButton("💰 Ga Kopen", callback_data="paper_buy")],
                [InlineKeyboardButton("🔙 Terug", callback_data="paper_trading_menu")]
            ]
        else:
            sell_text = """
💸 **PAPER TRADING - VERKOPEN** 💸

Selecteer een positie om te verkopen:

📊 **Je Posities:**
"""
            
            # Get current prices for user's positions
            symbols = list(portfolio['positions'].keys())
            prices = await self.price_provider.get_prices(symbols)
            
            keyboard = []
            for symbol in symbols:
                amount = portfolio['positions'][symbol]
                current_price = prices.get(symbol, 0)
                value = amount * current_price
                sell_text += f"• {symbol}: {amount:.4f} @ ${current_price:.4f} = ${value:.2f}\n"
                
                keyboard.append([InlineKeyboardButton(f"💸 Verkoop {symbol}", callback_data=f"paper_sell_{symbol}")])
            
            keyboard.append([InlineKeyboardButton("🔙 Terug", callback_data="paper_trading_menu")])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.callback_query.edit_message_text(sell_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def handle_buy_crypto(self, update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str):
        """Handle buying cryptocurrency"""
        user = update.effective_user
        user_id = user.id
        
        # Get current price
        current_price = await self.price_provider.get_price(symbol)
        
        buy_text = f"""
💰 **KOPEN: {symbol}** 💰

💵 **Huidige Prijs:** ${current_price:,.4f}

💡 **Kies een bedrag om te investeren:**
        """
        
        keyboard = [
            [InlineKeyboardButton("$100", callback_data=f"paper_execute_buy_{symbol}_100"),
             InlineKeyboardButton("$250", callback_data=f"paper_execute_buy_{symbol}_250")],
            [InlineKeyboardButton("$500", callback_data=f"paper_execute_buy_{symbol}_500"),
             InlineKeyboardButton("$1000", callback_data=f"paper_execute_buy_{symbol}_1000")],
            [InlineKeyboardButton("$2500", callback_data=f"paper_execute_buy_{symbol}_2500"),
             InlineKeyboardButton("$5000", callback_data=f"paper_execute_buy_{symbol}_5000")],
            [InlineKeyboardButton("🔙 Terug", callback_data="paper_buy")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(buy_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def handle_sell_crypto(self, update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str):
        """Handle selling cryptocurrency"""
        user_id = update.effective_user.id
        portfolio = await self.engine.get_portfolio(user_id)
        
        if not portfolio or symbol not in portfolio['positions']:
            await update.callback_query.answer("❌ Je hebt geen positie in deze cryptocurrency!")
            return
        
        current_amount = portfolio['positions'][symbol]
        current_price = await self.price_provider.get_price(symbol)
        total_value = current_amount * current_price
        
        sell_text = f"""
💸 **VERKOPEN: {symbol}** 💸

📊 **Je Positie:** {current_amount:.4f} {symbol}
💵 **Huidige Prijs:** ${current_price:,.4f}
💰 **Totale Waarde:** ${total_value:,.2f}

💡 **Kies hoeveel je wilt verkopen:**
        """
        
        # Calculate percentage amounts
        amounts = [
            (25, current_amount * 0.25),
            (50, current_amount * 0.50),
            (75, current_amount * 0.75),
            (100, current_amount)
        ]
        
        keyboard = []
        for percentage, amount in amounts:
            value = amount * current_price
            keyboard.append([InlineKeyboardButton(
                f"{percentage}% (${value:.2f})", 
                callback_data=f"paper_execute_sell_{symbol}_{amount:.8f}"
            )])
        
        keyboard.append([InlineKeyboardButton("🔙 Terug", callback_data="paper_sell")])
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(sell_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def execute_buy_order(self, update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str, usd_amount: float):
        """Execute a buy order"""
        user = update.effective_user
        user_id = user.id
        
        try:
            # Get current price and calculate amount
            current_price = await self.price_provider.get_price(symbol)
            crypto_amount = usd_amount / current_price
            
            # Place the order
            trade = await self.engine.place_order(user_id, symbol, 'buy', crypto_amount, current_price)
            
            success_text = f"""
✅ **KOOP ORDER UITGEVOERD!** ✅

📊 **Trade Details:**
• Symbol: {symbol}
• Bedrag: {crypto_amount:.6f} {symbol}
• Prijs: ${current_price:,.4f}
• Totaal: ${usd_amount:,.2f}
• Trade ID: #{trade['id']}

🎉 **Gefeliciteerd met je aankoop!**
            """
            
            keyboard = [
                [InlineKeyboardButton("📊 Bekijk Portfolio", callback_data="paper_portfolio")],
                [InlineKeyboardButton("💰 Meer Kopen", callback_data="paper_buy")],
                [InlineKeyboardButton("🔙 Trading Menu", callback_data="paper_trading_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.callback_query.edit_message_text(success_text, reply_markup=reply_markup, parse_mode='Markdown')
            
        except Exception as e:
            error_text = f"❌ **Fout bij kopen:** {str(e)}"
            await update.callback_query.answer(error_text)
    
    async def execute_sell_order(self, update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str, crypto_amount: float):
        """Execute a sell order"""
        user_id = update.effective_user.id
        
        try:
            # Get current price
            current_price = await self.price_provider.get_price(symbol)
            usd_value = crypto_amount * current_price
            
            # Place the order
            trade = await self.engine.place_order(user_id, symbol, 'sell', crypto_amount, current_price)
            
            success_text = f"""
✅ **VERKOOP ORDER UITGEVOERD!** ✅

📊 **Trade Details:**
• Symbol: {symbol}
• Bedrag: {crypto_amount:.6f} {symbol}
• Prijs: ${current_price:,.4f}
• Totaal: ${usd_value:,.2f}
• Trade ID: #{trade['id']}

💰 **Geld toegevoegd aan je balance!**
            """
            
            keyboard = [
                [InlineKeyboardButton("📊 Bekijk Portfolio", callback_data="paper_portfolio")],
                [InlineKeyboardButton("💸 Meer Verkopen", callback_data="paper_sell")],
                [InlineKeyboardButton("🔙 Trading Menu", callback_data="paper_trading_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.callback_query.edit_message_text(success_text, reply_markup=reply_markup, parse_mode='Markdown')
            
        except Exception as e:
            error_text = f"❌ **Fout bij verkopen:** {str(e)}"
            await update.callback_query.answer(error_text)

# Global instance
paper_trading_handler = PaperTradingHandler()
