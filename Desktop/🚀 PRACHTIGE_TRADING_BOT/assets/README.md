# Assets Directory

This directory contains media assets for the trading bot.

## Welcome Images

Place your welcome images here for the onboarding flow:

- `welcome_image.jpg` - Main welcome image shown to new users
- `premium_image.jpg` - Image shown for premium upgrade
- `community_image.jpg` - Image for community features

## Image Requirements

- Format: JPG, PNG, or GIF
- Max size: 10MB (Telegram limit)
- Recommended dimensions: 1280x720 for best display
- Ensure images are optimized for mobile viewing

## Usage

Images are referenced in the onboarding system and can be uploaded to:
1. Telegram servers (recommended for reliability)
2. External hosting services (imgur, etc.)
3. Local hosting (if you have a web server)

## Current Images

The onboarding system currently uses placeholder URLs. Replace these with actual image URLs in:
- `core/onboarding_manager.py`
- Update the `welcome_image_url` variable with your actual image URL
