"""
🚀 PRACHTIGE TRADING BOT - BROADCAST SYSTEM
==========================================

Automatisch broadcast systeem voor marktanalyse en trading berichten.
Inclusief interactieve trading knoppen en gebruikersbeheer.

Author: Innovars Lab
Version: 2.0.0
"""

import asyncio
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import random
from pathlib import Path

from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BroadcastSystem:
    """
    🚀 Automatisch Broadcast Systeem voor Trading Bot
    """

    def __init__(self):
        """Initialize broadcast system"""
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 Broadcast System wordt opgestart...")

        # Bot tokens
        self.main_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.community_token = os.getenv('COMMUNITY_BOT_TOKEN')

        if not self.main_token:
            self.logger.error("❌ TELEGRAM_BOT_TOKEN niet gevonden")
            return

        # Initialize bots
        self.main_bot = Bot(token=self.main_token)
        self.community_bot = Bot(token=self.community_token) if self.community_token else None

        # Data storage
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)

        self.subscribers_file = self.data_dir / "broadcast_subscribers.json"
        self.trading_sessions_file = self.data_dir / "trading_sessions.json"

        # Load data
        self.subscribers = self.load_subscribers()
        self.trading_sessions = self.load_trading_sessions()

        # Market data (mock data for demo)
        self.market_data = self.get_mock_market_data()

        self.logger.info("✅ Broadcast System geïnitialiseerd!")

    def load_subscribers(self) -> Dict:
        """Load broadcast subscribers"""
        try:
            if self.subscribers_file.exists():
                with open(self.subscribers_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not load subscribers: {e}")

        return {
            "users": {},
            "settings": {
                "daily_analysis": True,
                "trading_signals": True,
                "market_alerts": True
            }
        }

    def save_subscribers(self):
        """Save subscribers to file"""
        try:
            with open(self.subscribers_file, 'w', encoding='utf-8') as f:
                json.dump(self.subscribers, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"❌ Could not save subscribers: {e}")

    def load_trading_sessions(self) -> Dict:
        """Load active trading sessions"""
        try:
            if self.trading_sessions_file.exists():
                with open(self.trading_sessions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not load trading sessions: {e}")

        return {}

    def save_trading_sessions(self):
        """Save trading sessions to file"""
        try:
            with open(self.trading_sessions_file, 'w', encoding='utf-8') as f:
                json.dump(self.trading_sessions, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"❌ Could not save trading sessions: {e}")

    def get_mock_market_data(self) -> Dict:
        """Get mock market data for demo"""
        return {
            "BTC": {
                "price": 44250.00,
                "change_24h": 2.3,
                "volume": "28.5B",
                "trend": "bullish",
                "support": 43000,
                "resistance": 45500,
                "rsi": 68,
                "recommendation": "BUY"
            },
            "ETH": {
                "price": 2680.00,
                "change_24h": 1.8,
                "volume": "15.2B",
                "trend": "bullish",
                "support": 2600,
                "resistance": 2750,
                "rsi": 65,
                "recommendation": "BUY"
            },
            "ADA": {
                "price": 0.45,
                "change_24h": -3.2,
                "volume": "2.1B",
                "trend": "bearish",
                "support": 0.42,
                "resistance": 0.48,
                "rsi": 35,
                "recommendation": "SELL"
            },
            "SOL": {
                "price": 98.20,
                "change_24h": 8.7,
                "volume": "5.8B",
                "trend": "bullish",
                "support": 95,
                "resistance": 105,
                "rsi": 75,
                "recommendation": "BUY"
            }
        }

    def register_user(self, user_id: int, user_data: Dict):
        """Register user for broadcasts"""
        user_id_str = str(user_id)

        if user_id_str not in self.subscribers["users"]:
            self.subscribers["users"][user_id_str] = {
                "user_id": user_id,
                "username": user_data.get("username", ""),
                "first_name": user_data.get("first_name", ""),
                "registered_date": datetime.now().isoformat(),
                "preferences": {
                    "daily_analysis": True,
                    "trading_signals": True,
                    "market_alerts": True,
                    "preferred_pairs": ["BTC/USDT", "ETH/USDT"]
                },
                "trading_stats": {
                    "total_trades": 0,
                    "successful_trades": 0,
                    "total_volume": 0.0
                }
            }
            self.save_subscribers()
            self.logger.info(f"✅ User {user_id} registered for broadcasts")
            return True

        return False

    def unregister_user(self, user_id: int):
        """Unregister user from broadcasts"""
        user_id_str = str(user_id)

        if user_id_str in self.subscribers["users"]:
            del self.subscribers["users"][user_id_str]
            self.save_subscribers()
            self.logger.info(f"✅ User {user_id} unregistered from broadcasts")
            return True

        return False

    def get_daily_analysis_message(self) -> tuple:
        """Generate daily market analysis message"""
        current_time = datetime.now().strftime("%d-%m-%Y %H:%M")

        # Calculate market sentiment
        bullish_count = sum(1 for coin in self.market_data.values() if coin["trend"] == "bullish")
        total_coins = len(self.market_data)
        sentiment = "Bullish" if bullish_count > total_coins / 2 else "Bearish"
        sentiment_emoji = "🚀" if sentiment == "Bullish" else "📉"

        text = f"""
📊 **DAGELIJKSE MARKTANALYSE** 📊
🕐 **{current_time}**

{sentiment_emoji} **Markt Sentiment: {sentiment}**

💰 **TOP CRYPTOCURRENCIES:**

₿ **Bitcoin (BTC)**
├─ 💵 Prijs: ${self.market_data['BTC']['price']:,.2f}
├─ 📈 24h: {self.market_data['BTC']['change_24h']:+.1f}%
├─ 📊 Volume: {self.market_data['BTC']['volume']}
├─ 🎯 Support: ${self.market_data['BTC']['support']:,.0f}
├─ ⚠️ Resistance: ${self.market_data['BTC']['resistance']:,.0f}
├─ 📊 RSI: {self.market_data['BTC']['rsi']}
└─ 🎯 **Aanbeveling: {self.market_data['BTC']['recommendation']}**

⟠ **Ethereum (ETH)**
├─ 💵 Prijs: ${self.market_data['ETH']['price']:,.2f}
├─ 📈 24h: {self.market_data['ETH']['change_24h']:+.1f}%
├─ 📊 Volume: {self.market_data['ETH']['volume']}
├─ 🎯 Support: ${self.market_data['ETH']['support']:,.0f}
├─ ⚠️ Resistance: ${self.market_data['ETH']['resistance']:,.0f}
├─ 📊 RSI: {self.market_data['ETH']['rsi']}
└─ 🎯 **Aanbeveling: {self.market_data['ETH']['recommendation']}**

🔥 **Solana (SOL)**
├─ 💵 Prijs: ${self.market_data['SOL']['price']:,.2f}
├─ 📈 24h: {self.market_data['SOL']['change_24h']:+.1f}%
├─ 📊 Volume: {self.market_data['SOL']['volume']}
└─ 🎯 **Aanbeveling: {self.market_data['SOL']['recommendation']}**

📈 **TRADING KANSEN:**
🚀 **Klik hieronder om direct te traden!**
        """

        # Create interactive trading keyboard
        keyboard = [
            [
                InlineKeyboardButton("🟢 BUY BTC", callback_data="trade_buy_BTC"),
                InlineKeyboardButton("🔴 SELL BTC", callback_data="trade_sell_BTC")
            ],
            [
                InlineKeyboardButton("🟢 BUY ETH", callback_data="trade_buy_ETH"),
                InlineKeyboardButton("🔴 SELL ETH", callback_data="trade_sell_ETH")
            ],
            [
                InlineKeyboardButton("🟢 BUY SOL", callback_data="trade_buy_SOL"),
                InlineKeyboardButton("🔴 SELL SOL", callback_data="trade_sell_SOL")
            ],
            [
                InlineKeyboardButton("📊 Meer Analyse", callback_data="detailed_analysis"),
                InlineKeyboardButton("⚙️ Instellingen", callback_data="broadcast_settings")
            ]
        ]

        return text, InlineKeyboardMarkup(keyboard)

    async def send_daily_analysis(self):
        """Send daily analysis to all subscribers"""
        text, keyboard = self.get_daily_analysis_message()

        sent_count = 0
        failed_count = 0

        for user_id_str, user_data in self.subscribers["users"].items():
            if user_data["preferences"]["daily_analysis"]:
                try:
                    await self.main_bot.send_message(
                        chat_id=int(user_id_str),
                        text=text,
                        reply_markup=keyboard,
                        parse_mode=ParseMode.MARKDOWN
                    )
                    sent_count += 1
                    await asyncio.sleep(0.1)  # Rate limiting
                except Exception as e:
                    self.logger.warning(f"⚠️ Failed to send to {user_id_str}: {e}")
                    failed_count += 1

        self.logger.info(f"📤 Daily analysis sent: {sent_count} success, {failed_count} failed")
        return sent_count, failed_count

    def create_trading_session(self, user_id: int, action: str, symbol: str) -> str:
        """Create new trading session"""
        session_id = f"{user_id}_{action}_{symbol}_{int(datetime.now().timestamp())}"

        self.trading_sessions[session_id] = {
            "user_id": user_id,
            "action": action,  # "buy" or "sell"
            "symbol": symbol,  # "BTC", "ETH", etc.
            "pair": f"{symbol}/USDT",
            "created_at": datetime.now().isoformat(),
            "status": "amount_input",  # amount_input -> confirmation -> executed
            "amount": None,
            "price": self.market_data[symbol]["price"],
            "total_value": None
        }

        self.save_trading_sessions()
        return session_id

    def get_trading_amount_message(self, session_id: str) -> tuple:
        """Generate trading amount input message"""
        session = self.trading_sessions[session_id]
        action = session["action"].upper()
        symbol = session["symbol"]
        pair = session["pair"]
        current_price = session["price"]

        action_emoji = "🟢" if action == "BUY" else "🔴"

        text = f"""
{action_emoji} **{action} {symbol}** {action_emoji}

💰 **Trading Details:**
├─ 📊 Pair: {pair}
├─ 💵 Current Price: ${current_price:,.2f}
├─ 📈 24h Change: {self.market_data[symbol]['change_24h']:+.1f}%
└─ 🎯 Action: {action}

💸 **Voer je trading bedrag in:**

**Minimum:** $10
**Maximum:** $10,000

**Voorbeelden:**
• Type "100" voor $100
• Type "500" voor $500
• Type "1000" voor $1,000

⚠️ **Let op:** Dit bedrag wordt gebruikt voor je {action.lower()} order.
        """

        keyboard = [
            [
                InlineKeyboardButton("💰 $100", callback_data=f"amount_{session_id}_100"),
                InlineKeyboardButton("💰 $250", callback_data=f"amount_{session_id}_250")
            ],
            [
                InlineKeyboardButton("💰 $500", callback_data=f"amount_{session_id}_500"),
                InlineKeyboardButton("💰 $1000", callback_data=f"amount_{session_id}_1000")
            ],
            [
                InlineKeyboardButton("✏️ Custom Amount", callback_data=f"custom_amount_{session_id}"),
                InlineKeyboardButton("❌ Cancel", callback_data=f"cancel_{session_id}")
            ]
        ]

        return text, InlineKeyboardMarkup(keyboard)

    def get_trading_confirmation_message(self, session_id: str) -> tuple:
        """Generate trading confirmation message"""
        session = self.trading_sessions[session_id]
        action = session["action"].upper()
        symbol = session["symbol"]
        pair = session["pair"]
        amount = session["amount"]
        price = session["price"]
        total_value = session["total_value"]

        action_emoji = "🟢" if action == "BUY" else "🔴"

        # Calculate estimated coins/tokens
        if action == "BUY":
            estimated_coins = amount / price
            text = f"""
{action_emoji} **BEVESTIG JE {action} ORDER** {action_emoji}

📋 **Order Details:**
├─ 📊 Pair: {pair}
├─ 🎯 Action: {action}
├─ 💰 Amount: ${amount:,.2f}
├─ 💵 Price: ${price:,.2f}
├─ 🪙 Estimated {symbol}: {estimated_coins:.6f}
└─ 💸 Total Value: ${total_value:,.2f}

⚠️ **BELANGRIJK:**
• Deze order wordt direct uitgevoerd
• Controleer alle details zorgvuldig
• Zorg dat je voldoende saldo hebt

**Wil je deze order bevestigen?**
            """
        else:  # SELL
            text = f"""
{action_emoji} **BEVESTIG JE {action} ORDER** {action_emoji}

📋 **Order Details:**
├─ 📊 Pair: {pair}
├─ 🎯 Action: {action}
├─ 🪙 {symbol} Amount: {amount / price:.6f}
├─ 💵 Price: ${price:,.2f}
├─ 💰 Estimated USD: ${total_value:,.2f}
└─ 💸 Total Value: ${total_value:,.2f}

⚠️ **BELANGRIJK:**
• Deze order wordt direct uitgevoerd
• Controleer alle details zorgvuldig
• Zorg dat je voldoende {symbol} hebt

**Wil je deze order bevestigen?**
            """

        keyboard = [
            [
                InlineKeyboardButton("✅ BEVESTIG ORDER", callback_data=f"confirm_{session_id}"),
                InlineKeyboardButton("❌ ANNULEER", callback_data=f"cancel_{session_id}")
            ],
            [
                InlineKeyboardButton("🔙 Wijzig Bedrag", callback_data=f"back_amount_{session_id}")
            ]
        ]

        return text, InlineKeyboardMarkup(keyboard)

    async def execute_trade(self, session_id: str) -> tuple:
        """Execute the trading order (mock implementation)"""
        session = self.trading_sessions[session_id]

        # Mock trade execution
        success = random.choice([True, True, True, False])  # 75% success rate for demo

        if success:
            # Update session status
            session["status"] = "executed"
            session["executed_at"] = datetime.now().isoformat()
            session["order_id"] = f"ORDER_{random.randint(100000, 999999)}"

            # Update user stats
            user_id_str = str(session["user_id"])
            if user_id_str in self.subscribers["users"]:
                user_stats = self.subscribers["users"][user_id_str]["trading_stats"]
                user_stats["total_trades"] += 1
                user_stats["total_volume"] += session["total_value"]

                # 70% chance of successful trade for demo
                if random.choice([True, True, True, False, False]):
                    user_stats["successful_trades"] += 1

            self.save_trading_sessions()
            self.save_subscribers()

            return True, session["order_id"]
        else:
            return False, "Insufficient balance or market conditions"
