#!/usr/bin/env python3
"""
🧪 ONBOARDING SYSTEM TEST
========================

Test script for the progressive onboarding system.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.user_manager import UserManager, OnboardingStage
from core.onboarding_manager import OnboardingManager
from core.progressive_keyboards import ProgressiveKeyboards
from database.database_manager import DatabaseManager
from utils.logger import setup_logging

async def test_onboarding_system():
    """Test the onboarding system functionality"""
    
    # Setup logging
    logger = setup_logging()
    logger.info("🧪 Starting onboarding system test...")
    
    try:
        # Initialize components
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        user_manager = UserManager(db_manager)
        onboarding_manager = OnboardingManager(user_manager)
        progressive_keyboards = ProgressiveKeyboards(user_manager)
        
        # Test user ID
        test_user_id = 123456789
        
        logger.info("✅ Components initialized successfully")
        
        # Test 1: New user registration
        logger.info("🧪 Test 1: New user registration")
        user = await user_manager.register_user(
            user_id=test_user_id,
            username="test_user",
            first_name="Test",
            last_name="User"
        )
        
        assert user.onboarding_stage == OnboardingStage.NEW_USER
        logger.info("✅ New user registered with NEW_USER stage")
        
        # Test 2: Onboarding stage progression
        logger.info("🧪 Test 2: Onboarding stage progression")
        
        # Progress through stages
        stages = [
            OnboardingStage.WELCOME_SHOWN,
            OnboardingStage.DISCLAIMER_SHOWN,
            OnboardingStage.BASIC_FEATURES,
            OnboardingStage.PREMIUM_GATE,
            OnboardingStage.PREMIUM_MEMBER,
            OnboardingStage.COMPLETED
        ]
        
        for stage in stages:
            success = await user_manager.update_onboarding_stage(test_user_id, stage)
            assert success, f"Failed to update to stage {stage}"
            
            current_stage = await user_manager.get_onboarding_stage(test_user_id)
            assert current_stage == stage, f"Stage mismatch: expected {stage}, got {current_stage}"
            
            logger.info(f"✅ Successfully progressed to {stage.value}")
        
        # Test 3: Disclaimer acceptance
        logger.info("🧪 Test 3: Disclaimer acceptance")
        
        success = await user_manager.accept_disclaimer(test_user_id)
        assert success, "Failed to accept disclaimer"
        
        has_accepted = await user_manager.has_accepted_disclaimer(test_user_id)
        assert has_accepted, "Disclaimer acceptance not recorded"
        
        logger.info("✅ Disclaimer acceptance working correctly")
        
        # Test 4: Premium payment processing
        logger.info("🧪 Test 4: Premium payment processing")
        
        payment_ref = "TEST_PAYMENT_123"
        success = await user_manager.process_premium_payment(test_user_id, payment_ref, 49.0)
        assert success, "Failed to process premium payment"
        
        has_paid = await user_manager.has_paid_premium(test_user_id)
        assert has_paid, "Premium payment not recorded"
        
        payment_status = await user_manager.get_payment_status(test_user_id)
        assert payment_status == "paid", f"Payment status incorrect: {payment_status}"
        
        logger.info("✅ Premium payment processing working correctly")
        
        # Test 5: Progressive keyboards
        logger.info("🧪 Test 5: Progressive keyboards")
        
        # Test keyboards for different stages
        await user_manager.update_onboarding_stage(test_user_id, OnboardingStage.NEW_USER)
        keyboard = await progressive_keyboards.get_main_keyboard(test_user_id)
        assert keyboard is None, "New users should not have keyboards"
        
        await user_manager.update_onboarding_stage(test_user_id, OnboardingStage.BASIC_FEATURES)
        keyboard = await progressive_keyboards.get_main_keyboard(test_user_id)
        assert keyboard is not None, "Basic features users should have keyboards"
        
        await user_manager.update_onboarding_stage(test_user_id, OnboardingStage.PREMIUM_MEMBER)
        keyboard = await progressive_keyboards.get_main_keyboard(test_user_id)
        assert keyboard is not None, "Premium users should have keyboards"
        
        logger.info("✅ Progressive keyboards working correctly")
        
        # Test 6: User stats and information
        logger.info("🧪 Test 6: User stats and information")
        
        user_stats = await user_manager.get_user_stats(test_user_id)
        assert user_stats, "Failed to get user stats"
        assert user_stats['user_id'] == test_user_id, "User ID mismatch in stats"
        
        logger.info("✅ User stats retrieval working correctly")
        
        # Test 7: Admin and premium checks
        logger.info("🧪 Test 7: Admin and premium checks")
        
        is_admin = await user_manager.is_admin(test_user_id)
        is_premium = await user_manager.is_premium(test_user_id)
        is_vip = await user_manager.is_vip(test_user_id)
        
        logger.info(f"✅ User status checks: admin={is_admin}, premium={is_premium}, vip={is_vip}")
        
        # Cleanup
        logger.info("🧹 Cleaning up test data...")
        # In a real scenario, you might want to clean up test data
        
        logger.info("🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Close database connections
        if 'db_manager' in locals():
            await db_manager.close()

async def test_keyboard_generation():
    """Test keyboard generation for different user types"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 Testing keyboard generation...")
    
    try:
        # Initialize components
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        user_manager = UserManager(db_manager)
        progressive_keyboards = ProgressiveKeyboards(user_manager)
        
        # Test different user scenarios
        test_scenarios = [
            {"user_id": 1001, "stage": OnboardingStage.NEW_USER, "premium": False},
            {"user_id": 1002, "stage": OnboardingStage.BASIC_FEATURES, "premium": False},
            {"user_id": 1003, "stage": OnboardingStage.PREMIUM_MEMBER, "premium": True},
            {"user_id": 1004, "stage": OnboardingStage.COMPLETED, "premium": True},
        ]
        
        for scenario in test_scenarios:
            user_id = scenario["user_id"]
            stage = scenario["stage"]
            premium = scenario["premium"]
            
            # Create test user
            await user_manager.register_user(user_id, f"test_{user_id}", "Test", "User")
            await user_manager.update_onboarding_stage(user_id, stage)
            
            if premium:
                await user_manager.process_premium_payment(user_id, f"TEST_{user_id}", 49.0)
            
            # Test keyboard generation
            main_keyboard = await progressive_keyboards.get_main_keyboard(user_id)
            persistent_keyboard = await progressive_keyboards.get_persistent_keyboard(user_id)
            trading_keyboard = await progressive_keyboards.get_trading_keyboard(user_id)
            portfolio_keyboard = await progressive_keyboards.get_portfolio_keyboard(user_id)
            
            logger.info(f"✅ User {user_id} ({stage.value}, premium={premium}):")
            logger.info(f"   Main keyboard: {'Yes' if main_keyboard else 'No'}")
            logger.info(f"   Persistent keyboard: {'Yes' if persistent_keyboard else 'No'}")
            logger.info(f"   Trading keyboard: {'Yes' if trading_keyboard else 'No'}")
            logger.info(f"   Portfolio keyboard: {'Yes' if portfolio_keyboard else 'No'}")
        
        logger.info("🎉 Keyboard generation tests completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Keyboard test failed: {e}")
        return False
    
    finally:
        if 'db_manager' in locals():
            await db_manager.close()

def main():
    """Run all tests"""
    print("""
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║         🧪 ONBOARDING SYSTEM TEST SUITE 🧪                  ║
║                                                               ║
║    Testing progressive onboarding and keyboard systems       ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
    """)
    
    async def run_tests():
        success1 = await test_onboarding_system()
        success2 = await test_keyboard_generation()
        
        if success1 and success2:
            print("\n🎉 All tests passed! The onboarding system is ready to use.")
            return True
        else:
            print("\n❌ Some tests failed. Please check the logs and fix issues.")
            return False
    
    try:
        result = asyncio.run(run_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
